from sklearn.linear_model import Ridge
from sklearn.metrics import mean_squared_error, r2_score

class RidgeModel:
    def __init__(self, alpha=1.0):
        self.model = Ridge(alpha=alpha)

    def fit(self, X, y):
        self.model.fit(X, y)

    def predict(self, X):
        return self.model.predict(X)

    def evaluate(self, X, y):
        y_pred = self.predict(X)
        return {
            "RMSE": mean_squared_error(y, y_pred, squared=False),
            "R2": r2_score(y, y_pred)
        }
