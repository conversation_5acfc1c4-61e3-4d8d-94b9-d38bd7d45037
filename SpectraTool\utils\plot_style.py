import matplotlib.pyplot as plt

def apply_plot_style(ax, theme='light'):
    font_size = 12
    ax.title.set_fontsize(font_size + 2)
    ax.xaxis.label.set_fontsize(font_size)
    ax.yaxis.label.set_fontsize(font_size)
    ax.tick_params(axis='both', labelsize=font_size - 1)

    if theme == 'dark':
        ax.set_facecolor('#1e1e1e')
        ax.figure.set_facecolor('#2b2b2b')
        ax.title.set_color('white')
        ax.xaxis.label.set_color('white')
        ax.yaxis.label.set_color('white')
        ax.tick_params(colors='white')
        for spine in ax.spines.values():
            spine.set_color('white')
    else:
        ax.set_facecolor('white')
        ax.figure.set_facecolor('white')
        ax.title.set_color('black')
        ax.xaxis.label.set_color('black')
        ax.yaxis.label.set_color('black')
        ax.tick_params(colors='black')
        for spine in ax.spines.values():
            spine.set_color('black')
