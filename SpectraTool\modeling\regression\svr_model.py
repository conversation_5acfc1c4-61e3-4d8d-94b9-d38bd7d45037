from sklearn.svm import SVR
from sklearn.metrics import mean_squared_error, r2_score

class SVRModel:
    def __init__(self, kernel='rbf', C=1.0, epsilon=0.1):
        self.model = SVR(kernel=kernel, C=C, epsilon=epsilon)

    def fit(self, X, y):
        self.model.fit(X, y)

    def predict(self, X):
        return self.model.predict(X)

    def evaluate(self, X, y):
        y_pred = self.predict(X)
        return {
            "RMSE": mean_squared_error(y, y_pred, squared=False),
            "R2": r2_score(y, y_pred)
        }
