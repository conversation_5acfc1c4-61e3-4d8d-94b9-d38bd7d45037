from sklearn.svm import SVC
from sklearn.metrics import accuracy_score, confusion_matrix

class SVMClassifierModel:
    def __init__(self, kernel='rbf', C=1.0):
        self.model = SVC(kernel=kernel, C=C)

    def fit(self, X, y):
        self.model.fit(X, y)

    def predict(self, X):
        return self.model.predict(X)

    def evaluate(self, X, y):
        y_pred = self.predict(X)
        return {
            "Accuracy": accuracy_score(y, y_pred),
            "ConfusionMatrix": confusion_matrix(y, y_pred).tolist()
        }
