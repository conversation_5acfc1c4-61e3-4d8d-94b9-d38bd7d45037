# SpectraTool 光谱分析平台

一个基于 PyQt5 的近红外/高光谱数据建模平台，支持：
- 光谱预处理（SNV, MSC, SG 等）
- 波长选择（SPA）
- 多模型建模（Lasso, RF, SVM, XGB, MLP, ResNet 等）
- 模型评估（RMSE, R², 混淆矩阵）
- 批处理、一键建模
- 图像导出、报告输出
- 中英文界面、美观配色、暗黑主题

## 📦 安装依赖

```bash
pip install -r requirements.txt
```

## 🚀 运行 GUI

```bash
python main.py
```

## 🔧 打包为 EXE（Windows）

```bash
pyinstaller build.spec
```

> 执行后将在 dist/SpectraTool/ 目录下生成独立 SpectraTool.exe 文件。

## 📁 目录说明

- gui/ : 所有图形界面模块
- modeling/ : 所有建模算法
- preprocess/ : 光谱预处理
- wavelength_selection/ : 波长选择
- data/, output/ : 用户输入与输出
- assets/ : 图标与闪屏图像

## 📬 联系开发者

如果你在使用过程中有任何问题，请联系项目维护者。
