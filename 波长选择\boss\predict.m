function [RMSEP,RMSEF,Q2_test,Q2_train]=predict(Xcal,ycal,Xtest,ytest,selected_variables,optPC,method)

%++ prediction with the selected variables

if nargin<7;method='center';end;
if nargin<6;optPC=10;end;


Xcal=Xcal(:,selected_variables);
Xtest=Xtest(:,selected_variables);

[Xs,xpara1,xpara2]=pretreat(Xcal,method);
[ys,ypara1,ypara2]=pretreat(ycal,'center');
B=plsnipals(Xs,ys,optPC);
C=ypara2*B./xpara2';
coef=[C;ypara1-xpara1*C;];
Xcale=[Xcal ones(size(Xcal,1),1)];
yfit=Xcale*coef;
Xcale=[Xcal ones(size(Xcal,1),1)];
yfit=Xcale*coef;
Xteste=[Xtest ones(size(Xtest,1),1)];
ypred=Xteste*coef;
        
SST1=sum((ycal-mean(ycal)).^2);
SSE1=sum((ycal-yfit).^2);
SST=sum((ytest-mean(ytest)).^2);
SSE=sum((ytest-ypred).^2);
%%%%%%%%%   B E CAUTIOUS  ####################   
RMSEF=sqrt(sum((ycal-yfit).^2)/size(Xcal,1));
RMSEP=sqrt(sum((ytest-ypred).^2)/size(Xtest,1));
Q2_train=1-SSE1/SST1;
Q2_test=1-SSE/SST;