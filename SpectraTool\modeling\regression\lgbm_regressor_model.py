from lightgbm import LGBMRegressor
from sklearn.metrics import mean_squared_error, r2_score

class LGBMRegressorModel:
    def __init__(self, n_estimators=100, learning_rate=0.1):
        self.model = LGBMRegressor(n_estimators=n_estimators, learning_rate=learning_rate)

    def fit(self, X, y):
        self.model.fit(X, y)

    def predict(self, X):
        return self.model.predict(X)

    def evaluate(self, X, y):
        y_pred = self.predict(X)
        return {
            "RMSE": mean_squared_error(y, y_pred, squared=False),
            "R2": r2_score(y, y_pred)
        }
