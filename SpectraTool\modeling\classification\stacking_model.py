from sklearn.ensemble import StackingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.tree import DecisionTreeClassifier
from sklearn.metrics import accuracy_score, confusion_matrix

class StackingModel:
    def __init__(self):
        base_learners = [
            ('lr', LogisticRegression()),
            ('dt', DecisionTreeClassifier())
        ]
        self.model = StackingClassifier(estimators=base_learners, final_estimator=LogisticRegression())

    def fit(self, X, y):
        self.model.fit(X, y)

    def predict(self, X):
        return self.model.predict(X)

    def evaluate(self, X, y):
        y_pred = self.predict(X)
        return {
            "Accuracy": accuracy_score(y, y_pred),
            "ConfusionMatrix": confusion_matrix(y, y_pred).tolist()
        }
