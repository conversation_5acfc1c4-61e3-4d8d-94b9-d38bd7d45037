import numpy as np

def msc(input_data):
    """
    多元散射校正（MSC）
    参数:
        input_data: 2D numpy array (samples x variables)
    返回:
        corrected_data: MSC 处理后的数据
    """
    mean_spectrum = np.mean(input_data, axis=0)
    corrected_data = np.zeros_like(input_data)
    for i in range(input_data.shape[0]):
        fit = np.polyfit(mean_spectrum, input_data[i, :], 1, full=True)
        corrected_data[i, :] = (input_data[i, :] - fit[0][1]) / fit[0][0]
    return corrected_data
