%% 清空
clc
clear

%% 导入数据 运行前先把数据格式调整，第一行为全波长点，行为样品，列为波长反射率，最后一列为理化值y
Data = xlsread('小麦248.xlsx');
data = Data(2:end,:);  %光谱阵和理化值
nm = Data(1,1:end-1);  %读取波长数据，确保维度匹配
X = data(:,1:end-1);    %光谱阵
y = data(:,end);       %理化值

%% IRF
%参数设置
N = 1000;
w = 100; %移动窗口长度 原始波长点数*0.05
Q = 20;
A = 10;
method='center';

%波长选择
F=irf(X,y,N,w,Q,A,method);
nm_num = 10; %选择概率前10的波长 根据自己需要改
SelectedVariables = F.vsel(1:nm_num); %筛选的特征波长索引
nm_sel = nm(SelectedVariables);%筛选的特征波长

fprintf('特征波长为：%s\n', num2str(nm_sel));
fprintf('特征波长数为：%d\n', length(nm_sel));

%% 绘图
figure,hold,grid off;
plot(nm,X(1,:),'color', '#2878b5','LineWidth', 1);
plot(nm_sel,X(1,SelectedVariables),'ko', 'MarkerSize', 4, 'MarkerFaceColor', 'r')
xlim([730,1100]);
set(gca,'FontSize',12,'Fontname', 'Times New Roman','LineWidth',1,'FontWeight','bold'); 
xlabel('Wavelength (nm)');
ylabel('Reflectance');
box on;

%% 特征波长数据集
X = X(:,SelectedVariables);
