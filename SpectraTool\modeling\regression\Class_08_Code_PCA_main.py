#!/usr/bin/env python 
# -*- coding:utf-8 -*-
__author__ = '<PERSON><PERSON> @ Shanxi University'

from sklearn.decomposition import PCA
import scipy.io as sio
import numpy as np
import matplotlib.pyplot as plt
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_squared_error
from sklearn.metrics import r2_score
from sklearn.linear_model import LinearRegression

# 导入数据集
file = 'spectra_data.mat'
data = sio.loadmat(file)
X = data['NIR']
Y = data['octane']
print(X.shape)
print(Y.shape)

# 划分训练集/测试集
# k = np.random.permutation(X.shape[0])
k = np.arange(60)
print(k)
X_train = X[k[:50], :]      # 训练集
Y_train = Y[k[:50], :]

X_test = X[k[50:], :]       # 测试集
Y_test = Y[k[50:], :]

n = Y_test.shape[0]

print(X_train.shape)
print(X_test.shape)
print(Y_train.shape)
print(Y_test.shape)

# 数据归一化
mms = MinMaxScaler()
X_train_mms = mms.fit_transform(X_train)
X_test_mms = mms.transform(X_test)

Y_train_mms = mms.fit_transform(Y_train)

# 主成分分析
pca = PCA()
X_train_pca = pca.fit_transform(X_train)
print(pca.explained_variance_ratio_.shape)

plt.bar(np.arange(5), pca.explained_variance_ratio_[:5], alpha=0.5, align='center')
plt.step(np.arange(5), np.cumsum(pca.explained_variance_ratio_[:5]), where='mid')
plt.ylabel('Explained variance ratio')
plt.xlabel('Principal components')

plt.show()

# 特征变换
pca = PCA(n_components=4)
X_train_pca = pca.fit_transform(X_train_mms)
X_test_pca = pca.transform(X_test_mms)
print(X_train_pca.shape)
print(X_test_pca.shape)

# 绘图
plt.figure()
plt.scatter(x=X_train_pca[:, 0],
            y=X_train_pca[:, 1],
            c='r',
            marker='s',
            label='Train set')
plt.scatter(x=X_test_pca[:, 0],
            y=X_test_pca[:, 1],
            c='b',
            marker='o',
            label='Test set')
plt.xlabel('PC 1')
plt.ylabel('PC 2')
plt.legend(loc='upper left')
plt.tight_layout()
plt.show()

# 建立多元线性回归模型
linreg = LinearRegression()

# 训练多元线性回归模型
model = linreg.fit(X_train_pca, Y_train_mms)
print(X_train_pca.shape)
print(model)

# 训练后模型截距
print(model.intercept_)
# 训练后模型权重（特征个数无变化）
print(model.coef_)

# 多元线性回归模型预测
Y_sim_mms = model.predict(X_test_pca)

# 反归一化
Y_sim = mms.inverse_transform(Y_sim_mms.reshape(n, 1))
print(Y_sim)

# 结果
Result = np.hstack((Y_test, Y_sim))
MSE = mean_squared_error(Y_test, Y_sim)
R2 = r2_score(Y_test, Y_sim)

print(Result)
print('Prediction Mean Squared Error (MSE) is {:f}'.format(MSE))
print('Prediction Determined Coefficient R2 is {:f}'.format(R2))

# 绘图
plt.rcParams['font.sans-serif']=['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.figure()
plt.scatter(Y_test, Y_sim)
plt.xlabel('真实值')
plt.ylabel('预测值')
plt.title('汽油辛烷值预测结果（R2={:f})'.format(R2))
plt.plot(Y_test, Y_test, 'r')
plt.show()
