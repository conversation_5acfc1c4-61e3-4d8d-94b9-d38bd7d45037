from modeling import MODEL_REGISTRY
from PyQt5.QtWidgets import QComboBox, QMessageBox, (
    QWidget, QVBoxLayout, QPushButton, QFileDialog, QLabel, QCheckBox
)
from sklearn.model_selection import train_test_split
from sklearn.metrics import r2_score, mean_squared_error
from sklearn.preprocessing import StandardScaler
from sklearn.cross_decomposition import PLSRegression
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from utils.plot_style import apply_plot_style
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from wavelength_selection.spa import spa

class OneClickModelTab(QWidget):
    def __init__(self):
        super().__init__()
        self.X = None
        self.y = None
        self.selected_idx = None
        self.model = None
        self.y_pred = None

        layout = QVBoxLayout()

        self.cb_preprocess = QCheckBox("预处理（标准化）")
        self.cb_select = QCheckBox("波长选择（SPA）")
        self.cb_model = QCheckBox("建模（PLSR）")
        self.cb_preprocess.setChecked(True)
        self.cb_model.setChecked(True)

        load_btn = QPushButton("导入 X / y 数据")
        load_btn.clicked.connect(self.load_data)

        run_btn = QPushButton("一键执行")
        run_btn.clicked.connect(self.run_pipeline)

        self.canvas = FigureCanvas(plt.Figure(figsize=(6, 4)))

        # 导出图像按钮
        export_btn = QPushButton("导出图像")
        export_btn.clicked.connect(self.export_figure)
        self.ax = self.canvas.figure.subplots()

        layout.addWidget(self.cb_preprocess)
        layout.addWidget(self.cb_select)
        layout.addWidget(self.cb_model)
        layout.addWidget(load_btn)
        layout.addWidget(run_btn)
        layout.addWidget(export_btn)

        # 模型选择下拉框
        self.model_selector = QComboBox()
        self.model_selector.addItems(list(MODEL_REGISTRY.keys()))
        layout.addWidget(self.model_selector)

        # 导出报告按钮
        report_btn = QPushButton("导出建模报告")
        report_btn.clicked.connect(self.export_report)
        layout.addWidget(report_btn)
        layout.addWidget(self.canvas)
        self.setLayout(layout)

    def load_data(self):
        x_path, _ = QFileDialog.getOpenFileName(self, "选择 X 数据", "", "CSV Files (*.csv)")
        y_path, _ = QFileDialog.getOpenFileName(self, "选择 y 数据", "", "CSV Files (*.csv)")
        if x_path and y_path:
            x_df = pd.read_csv(x_path)
            y_df = pd.read_csv(y_path)
            self.X = x_df.to_numpy()
            self.y = y_df.to_numpy().ravel()

    def run_pipeline(self):
        if self.X is None or self.y is None:
            return

        X = self.X.copy()
        y = self.y.copy()

        if self.cb_preprocess.isChecked():
            X = StandardScaler().fit_transform(X)

        if self.cb_select.isChecked():
            idx = spa(X, y, n_selected=10)
            X = X[:, idx]
            self.selected_idx = idx

        if self.cb_model.isChecked():
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)
            model = PLSRegression(n_components=10)
            model.fit(X_train, y_train)
            y_pred = model.predict(X_test).ravel()
            rmse = mean_squared_error(y_test, y_pred, squared=False)
            r2 = r2_score(y_test, y_pred)

            self.ax.clear()
            self.ax.scatter(y_test, y_pred, alpha=0.6)
            self.ax.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--')
            self.ax.set_title(f"一键建模结果
RMSE={rmse:.3f}, R²={r2:.3f}")
            self.ax.set_xlabel("真实值")
            self.ax.set_ylabel("预测值")
        QMessageBox.information(self, '成功', '一键建模完成')
            apply_plot_style(self.ax, theme="light")
            self.canvas.draw()


    def export_figure(self):
        if self.canvas:
            path, _ = QFileDialog.getSaveFileName(self, "保存图像", "oneclick_plot", "图像文件 (*.png *.pdf *.svg)")
            if path:
                self.canvas.figure.savefig(path, bbox_inches='tight', dpi=300)


    def export_report(self):
        from datetime import datetime
        import base64
        import io

        if self.y_pred is None:
            return

        now = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # 保存图像为 base64 嵌入 HTML
        buf = io.BytesIO()
        self.canvas.figure.savefig(buf, format='png', bbox_inches='tight', dpi=300)
        image_base64 = base64.b64encode(buf.getvalue()).decode("utf-8")

        html = f"""
        <html><head><meta charset="utf-8"><title>建模报告</title></head><body>
        <h2>建模报告 - SpectraTool</h2>
        <p><b>时间：</b> {now}</p>
        <p><b>预处理：</b> {'标准化' if self.cb_preprocess.isChecked() else '无'}</p>
        <p><b>变量选择：</b> {'SPA' if self.cb_select.isChecked() else '无'}</p>
        <p><b>模型：</b> PLSRegression (n_components=10)</p>
        <p><b>变量数量：</b> {self.X.shape[1]}</p>
        <p><b>RMSE：</b> {mean_squared_error(self.y, self.model.predict(self.X).ravel(), squared=False):.4f}</p>
        <p><b>R²：</b> {r2_score(self.y, self.model.predict(self.X).ravel()):.4f}</p>
        <h3>预测图</h3>
        <img src="data:image/png;base64,{image_base64}" style="max-width:600px">
        </body></html>
        """

        path, _ = QFileDialog.getSaveFileName(self, "保存报告", "report.html", "HTML 文件 (*.html)")
        if path:
            with open(path, "w", encoding="utf-8") as f:
                f.write(html)
