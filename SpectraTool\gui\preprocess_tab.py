from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QPushButton, QFileDialog, QCheckBox, QLabel, QHBoxLayout
)
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from preprocess.msc import msc
from preprocess.snv import snv
from preprocess.savgol import savgol

class PreprocessTab(QWidget):
    def __init__(self):
        super().__init__()
        self.data = None
        self.processed_data = None
        self.wavelengths = None

        layout = QVBoxLayout()

        # 控件：加载按钮 + 复选框
        load_btn = QPushButton("导入数据文件（.csv）")
        load_btn.clicked.connect(self.load_data)

        self.cb_msc = QCheckBox("MSC")
        self.cb_snv = QCheckBox("SNV")
        self.cb_sg = QCheckBox("Savitzky-Golay 滤波")

        process_btn = QPushButton("开始预处理")
        process_btn.clicked.connect(self.run_preprocessing)

        # 布局
        control_layout = QHBoxLayout()
        control_layout.addWidget(self.cb_msc)
        control_layout.addWidget(self.cb_snv)
        control_layout.addWidget(self.cb_sg)
        control_layout.addStretch()

        layout.addWidget(load_btn)
        layout.addLayout(control_layout)
        layout.addWidget(process_btn)

        # 图像绘图区域
        self.canvas = FigureCanvas(plt.Figure(figsize=(8, 4)))
        layout.addWidget(self.canvas)
        self.ax = self.canvas.figure.subplots()

        self.setLayout(layout)

    def load_data(self):
        path, _ = QFileDialog.getOpenFileName(self, "选择光谱数据", "", "CSV Files (*.csv)")
        if path:
            df = pd.read_csv(path)
            self.wavelengths = df.columns.astype(float)
            self.data = df.to_numpy()
            self.plot_data(self.data, title="原始光谱")

    def run_preprocessing(self):
        if self.data is None:
            return
        X = self.data.copy()
        if self.cb_msc.isChecked():
            X = msc(X)
        if self.cb_snv.isChecked():
            X = snv(X)
        if self.cb_sg.isChecked():
            X = savgol(X, window_length=11, polyorder=2)
        self.processed_data = X
        self.plot_data(X, title="预处理后光谱")

    def plot_data(self, data, title=""):
        self.ax.clear()
        for row in data:
            self.ax.plot(self.wavelengths, row)
        self.ax.set_title(title)
        self.ax.set_xlabel("波长")
        self.ax.set_ylabel("吸光度")
        self.canvas.draw()
