import torch
import torch.nn as nn
import torchvision.models as models
from sklearn.metrics import accuracy_score, confusion_matrix

class ResNetTransferModel:
    def __init__(self, num_classes=2):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model = models.resnet18(pretrained=True)
        self.model.fc = nn.Linear(self.model.fc.in_features, num_classes)
        self.model = self.model.to(self.device)
        self.criterion = nn.CrossEntropyLoss()
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=1e-4)

    def fit(self, X, y):
        # X: (N, 3, H, W) tensor, y: (N,) tensor
        self.model.train()
        for epoch in range(10):
            outputs = self.model(X.to(self.device))
            loss = self.criterion(outputs, y.to(self.device))
            self.optimizer.zero_grad()
            loss.backward()
            self.optimizer.step()

    def predict(self, X):
        self.model.eval()
        with torch.no_grad():
            preds = self.model(X.to(self.device))
        return preds.argmax(dim=1).cpu()

    def evaluate(self, X, y):
        y_pred = self.predict(X)
        return {
            "Accuracy": accuracy_score(y.cpu(), y_pred),
            "ConfusionMatrix": confusion_matrix(y.cpu(), y_pred).tolist()
        }
