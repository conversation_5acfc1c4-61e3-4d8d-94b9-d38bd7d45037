import torch
import numpy as np

# 构造一个5x3矩阵
x = torch.empty(5, 3)
print(x)

# 判断变量类型是否为tensor
print(torch.is_tensor(x))

# 构造一个随机初始化的矩阵
x = torch.rand(5, 3)
print(x)

x = torch.randn(5, 3)
print(x)

x = torch.randperm(5)
print(x)

# 创建全0、全1 Tensor
x = torch.zeros(5, 3, dtype=torch.long)
print(x)

x = torch.eye(3)              # Create an identity 3x3 tensor
print(x)

x = torch.ones(10)              # A tensor of size 10 containing all ones
print(x)

x = torch.ones(2, 1, 2, 1)      # Size 2x1x2x1
print(x)

v = torch.ones_like(x)        # A tensor with same shape as eye. Fill it with 1.

x = torch.zeros(10)             # A tensor of size 10 containing all zeros
print(x)


# 构造一个张量，直接使用数据
x = torch.tensor([5.5, 3])
print(x)

v = torch.arange(5)             # similar to range(5) but creating a Tensor
v = torch.arange(0, 5, step=1)  # Size 5. Similar to range(0, 5, 1)
print(v)

v = torch.arange(12)
v = v.view(3, 4)
print(v)

v = v.view(-1, 2)
print(v)

# 初始化一个线性或对数的张量
v = torch.linspace(1, 10, steps=10) # Create a Tensor with 10 linear points for (1, 10) inclusively
print(v)

v = torch.logspace(start=-10, end=10, steps=5) # Size 5: 1.0e-10 1.0e-05 1.0e+00, 1.0e+05, 1.0e+10
print(v)

# 创建一个 tensor 基于已经存在的 tensor
x = x.new_ones(5, 3, dtype=torch.double)
print(x)

y = torch.randn_like(x, dtype=torch.float)    # override dtype!
print(y)                                      # result has the same size

# 获取维度信息
print(x.size())
print(torch.numel(x))   # number of elements in x


# 加法操作一
y = torch.rand(5, 3)
print(x + y)

# 加法操作二
print(torch.add(x, y))

# 加法操作三
result = torch.empty(5, 3)
torch.add(x, y, out=result)
print(result)

# 加法操作四
y.add_(x)
print(y)

# 索引操作
print(x[:, 1])

# 改变大小
x = torch.randn(4, 4)
y = x.view(16)
z = x.view(-1, 8)  # the size -1 is inferred from other dimensions
print(x.size(), y.size(), z.size())


# 获取元素的值
x = torch.randn(1)
print(x)
print(x.item())

# Numpy与Tensor之间的转换
a = np.array([1, 2, 3])
v = torch.from_numpy(a)         # Convert a numpy array to a Tensor

b = v.numpy()                   # Tensor to numpy
b[1] = -1                       # Numpy and Tensor share the same memory
assert(a[1] == b[1])            # Change Numpy will also change the Tensor

# 张量的拼接
# Concatenation
x = torch.randn(2, 3)
y = torch.cat((x, x, x), 0)          # Concatenate in the 0 dimension
print(y)

# Stack
r = torch.stack((x, x))
print(r)

# 张量的分裂
r = torch.chunk(v, 3)
print(r)

r = torch.split(v, 2)
print(r)

# squenze & unsequenze
t = torch.ones(2,1,2,1) # Size 2x1x2x1
r = torch.squeeze(t)     # Size 2x2
r = torch.squeeze(t, 1)  # Squeeze dimension 1: Size 2x2x1

# Un-squeeze a dimension
x = torch.Tensor([1, 2, 3])
r = torch.unsqueeze(x, 0)       # Size: 1x3
r = torch.unsqueeze(x, 1)       # Size: 3x1

# 非零元素
v = torch.tensor([0,1,1,0,0,1,1])
r = torch.nonzero(v)
print(r)

# 转置
x = torch.randn(2, 4)
r = torch.transpose(x, 0, 1)
print(r)

# 随机分布
# 2x2: A uniform distributed random matrix with range [0, 1]
r = torch.Tensor(2, 2).uniform_(0, 1)

# bernoulli
r = torch.bernoulli(r)   # Size: 2x2. Bernoulli with probability p stored in elements of r

# Multinomial
w = torch.Tensor([0, 4, 8, 2]) # Create a tensor of weights
r = torch.multinomial(w, 4, replacement=True) # Size 4: 3, 2, 1, 2

# Normal distribution
# From 10 means and SD
r = torch.normal(means=torch.arange(1, 11), std=torch.arange(1, 0.1, -0.1)) # Size 10


# Point-wise operations
### Math operations
f= torch.FloatTensor([-1, -2, 3])
r = torch.abs(f)      # 1 2 3

# Add x, y and scalar 10 to all elements
r = torch.add(x, 10)
r = torch.add(x, 10, y)

# Clamp the value of a Tensor
r = torch.clamp(f, min=-0.5, max=0.5)

# Element-wise divide
r = torch.div(v, v+0.03)

# Element-wise multiple
r = torch.mul(v, v)

# Reduction operations
r = torch.cumsum(v, dim=0)

# L-P norm
r = torch.dist(v, v+3, p=2)  # L-2 norm: ((3^2)*9)^(1/2) = 9.0

# Mean
r = torch.mean(v, 1)         # Size 3: Mean in dim 1

r = torch.mean(v, 1, True)   # Size 3x1 since keep dimension = True

# Sum
r = torch.sum(v, 1)          # Sum over dim 1

r = torch.sum(v)

# Comparison operation
r = torch.eq(v, v)
r = torch.max(v, 1)

# 排序
r = torch.sort(v, 1)

# k-th and top k
r = torch.kthvalue(v, 2)
r = torch.topk(v, 1)

# Matrix, vector multiplication
# Dot product of 2 tensors
r = torch.dot(torch.Tensor([4, 2]), torch.Tensor([3, 1]))

# Matrix X vector
mat = torch.randn(2, 4)
vec = torch.randn(4)
r = torch.mv(mat, vec)

# Matrix + Matrix X vector
M = torch.randn(2)
mat = torch.randn(2, 3)
vec = torch.randn(3)
r = torch.addmv(M, mat, vec)

# Matrix x Matrix
mat1 = torch.randn(2, 3)
mat2 = torch.randn(3, 4)
r = torch.mm(mat1, mat2)

# Matrix + Matrix X Matrix
M = torch.randn(3, 4)
mat1 = torch.randn(3, 2)
mat2 = torch.randn(2, 4)
r = torch.addmm(M, mat1, mat2)

# Diagonal matrix
r = torch.diag(v)

# Histogram
torch.histc(torch.FloatTensor([1, 2, 1]), bins=4, min=0, max=3)

# 自动求导
x = torch.tensor(1.0)
w = torch.tensor(1.0, requires_grad=True)
y = torch.tensor(2.0)

loss = (x*w - y)**2

loss.backward()

print(w.grad)