import torch
import torch.nn as nn
from sklearn.metrics import accuracy_score, confusion_matrix

class TransferMLPModel:
    def __init__(self, input_dim=256, hidden_dim=64, num_classes=2):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, num_classes)
        ).to(self.device)
        self.criterion = nn.CrossEntropyLoss()
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=1e-4)

    def fit(self, X, y):
        self.model.train()
        for epoch in range(10):
            outputs = self.model(X.to(self.device))
            loss = self.criterion(outputs, y.to(self.device))
            self.optimizer.zero_grad()
            loss.backward()
            self.optimizer.step()

    def predict(self, X):
        self.model.eval()
        with torch.no_grad():
            return self.model(X.to(self.device)).argmax(dim=1).cpu()

    def evaluate(self, X, y):
        y_pred = self.predict(X)
        return {
            "Accuracy": accuracy_score(y.cpu(), y_pred),
            "ConfusionMatrix": confusion_matrix(y.cpu(), y_pred).tolist()
        }
