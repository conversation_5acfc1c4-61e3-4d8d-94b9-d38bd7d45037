#!/usr/bin/env python 
# -*- coding:utf-8 -*-
__author__ = '<PERSON><PERSON> @ Shanxi University'

import math

# region 条件判断
print("条件判断：")
age = 5
if age >= 18:
    print('Your age is: ', age)
    print('adult')
elif age >= 6:
    print('Your age is: ', age)
    print('teenager')
else:
    print('Your age is: ', age)
    print('kid')

s = input('Birth year: ')
birth_year = int(s)
if birth_year < 2000:
    print("00前")
else:
    print("00后")
#endregion

# region for循环
print("for循环：")
names = ['<PERSON>', '<PERSON>', '<PERSON>']
for name in names:
    print(name)

sum = 0
for x in [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]:
    sum = sum + x
print(sum)

sum = 0
for x in range(101):
    sum = sum + x
print(sum)
# endregion

# region while循环
print("while循环：")
sum = 0
n = 99
while n > 0:
    sum = sum + n
    n = n - 2
print(sum)
# endregion

# region break
print("break: ")
n = 1
while n <= 100:
    if n > 10: # 当n = 11时，条件满足，执行break语句
        break # break语句会结束当前循环
    print(n)
    n = n + 1
print('END')
# endregion

# region continue
print("continue: ")
n = 0
while n < 10:
    n = n + 1
    if n % 2 == 0: # 如果n是偶数，执行continue语句
        continue # continue语句会直接继续下一轮循环，后续的print()语句不会执行
    print(n)
# endregion

# region 函数
print("函数：")
def my_abs(x):
    if not isinstance(x, (int, float)):
        raise TypeError('bad operand type')
    if x >= 0:
        return x
    else:
        return -x

print(my_abs(-99))
print(my_abs('A'))


# 返回多个值
def move(x, y, step, angle=0):
    nx = x + step * math.cos(angle)
    ny = y - step * math.sin(angle)
    return nx, ny


x, y = move(100, 100, 60, math.pi / 6)
print(x, y)

r = move(100, 100, 60, math.pi / 6)
print(r)    # 本质上返回值是个tuple


# 多个输入参数
def power(x, n=2):
    s = 1
    while n > 0:
        n = n - 1
        s = s * x
    return s


print(power(5))
print(power(5, 2))


# 可变参数
def calc(numbers):
    sum = 0
    for n in numbers:
        sum = sum + n * n
    return sum


print(calc([1, 2, 3]))      # 先把输入参数组装成一个list或tuple
print(calc((1, 3, 5, 7)))


def calc(*numbers):
    sum = 0
    for n in numbers:
        sum = sum + n * n
    return sum


print(calc(1, 2, 3))
print(calc(1, 3, 5, 7))
nums = [1, 2, 3]
print(calc(*nums))

# 关键字参数
def person(name, age, **kw):
    print('name:', name, 'age:', age, 'other:', kw)


person('Michael', 30)
person('Bob', 35, city='Beijing')
person('Adam', 45, gender='M', job='Engineer')

extra = {'city': 'Beijing', 'job': 'Engineer'}
person('Jack', 24, **extra)
# endregion

# region 匿名函数
sum_new = lambda arg1, arg2: arg1 + arg2
print("相加后的值为 : ", sum_new(10, 20))
print("相加后的值为 : ", sum_new(20, 20))
# endregion

# region 全局变量和局部变量
total = 0  # 这是一个全局变量


def sum(arg1, arg2):
    # 返回2个参数的和
    total = arg1 + arg2  # total在这里是局部变量.
    print("函数内是局部变量 : ", total)
    return total

# 调用sum函数
sum(10, 20)
print("函数外是全局变量 : ", total)
# endregion


# region 类的创建与实例化
class Employee:
    '所有员工的基类'
    empCount = 0

    def __init__(self, name, salary):
        self.name = name
        self.salary = salary
        Employee.empCount += 1

    def displayCount(self):
        print("Total Employee %d" % Employee.empCount)

    def displayEmployee(self):
        print("Name : ", self.name, ", Salary: ", self.salary)


# 创建 Employee 类的第一个对象
emp1 = Employee("Zara", 2000)

# 创建 Employee 类的第二个对象
emp2 = Employee("Manni", 5000)

emp1.displayEmployee()
emp2.displayEmployee()
print("Total Employee %d" % Employee.empCount)

# 添加、修改、删除属性
emp1.age = 7  # 添加一个 'age' 属性
print("Age: ", emp1.age)

emp1.age = 8  # 修改 'age' 属性
print("Age: ", emp1.age)

del emp1.age  # 删除 'age' 属性
print("Age: ", emp1.age)

# 属性访问
hasattr(emp1, 'age')    # 如果存在 'age' 属性返回 True。
getattr(emp1, 'age')    # 返回 'age' 属性的值
setattr(emp1, 'age', 10) # 添加属性 'age' 值为 8
delattr(emp1, 'age')    # 删除属性 'age'

# Python内置类属性调用
print("Employee.__doc__:", Employee.__doc__)
print("Employee.__name__:", Employee.__name__)
print("Employee.__module__:", Employee.__module__)
print("Employee.__bases__:", Employee.__bases__)
print("Employee.__dict__:", Employee.__dict__)
# endregion