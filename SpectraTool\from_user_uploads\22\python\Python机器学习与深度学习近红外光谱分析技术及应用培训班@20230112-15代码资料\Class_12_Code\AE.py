import scipy.io as sio
import numpy as np
from sklearn import preprocessing
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, r2_score
import matplotlib.pyplot as plt

import torch
from torch import nn
from torch.utils.data import DataLoader, Dataset


# 导入数据
file = 'spectra_data.mat'
data = sio.loadmat(file)
X = data['NIR']
Y = data['octane']

# 拆分训练集、测试集
k = np.random.permutation(X.shape[0])
print(k)
X_train = X[k[:50], :]
Y_train = Y[k[:50], :]

X_test = X[k[50:], :]
Y_test = Y[k[50:], :]

# 归一化预处理
mms = preprocessing.MinMaxScaler()
X_train = mms.fit_transform(X_train)
X_test = mms.transform(X_test)

Y_train = mms.fit_transform(Y_train)

print(X_train.shape)
print(Y_train.shape)
print(X_test.shape)
print(Y_test.shape)

# 转换成tensor格式
xtrain = torch.unsqueeze(torch.tensor(X_train).float(), dim=1)   #升维
xtest = torch.unsqueeze(torch.tensor(X_test).float(), dim=1)
print(xtrain.shape)
ytrain = torch.tensor(Y_train).float()
ytest = torch.tensor(Y_test).float()


class DatasetXY(Dataset):
    def __init__(self, x, y):
        self._x = x
        self._y = y
        self._len = len(x)

    def __getitem__(self, item):  # 每次循环的时候返回的值
        return self._x[item], self._y[item]

    def __len__(self):
        return self._len


batchsize = 5
train_loader = DataLoader(DatasetXY(xtrain, ytrain), batch_size=batchsize, shuffle=False, drop_last=True, num_workers=0)

#device : GPU or CPU
device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
print(device)



num_epochs = 200
batch_size = 128
learning_rate = 1e-3


class AutoEncoder(nn.Module):
    def __init__(self):
        super(AutoEncoder, self).__init__()
        self.encoder = nn.Sequential(
            nn.Linear(401, 100),
            nn.ReLU(True),
            nn.Linear(100, 20),
            nn.ReLU(True))
        self.decoder = nn.Sequential(
            nn.Linear(20, 100),
            nn.ReLU(True),
            nn.Linear(100, 401),
            nn.Sigmoid())

    def forward(self, x):
        x = self.encoder(x)
        x = self.decoder(x)
        return x


model = AutoEncoder()
model.to(device)
criterion = nn.MSELoss()
optimizer = torch.optim.Adam(
    model.parameters(), lr=learning_rate, weight_decay=1e-5)

for epoch in range(num_epochs):
    for data in train_loader:
        x, _ = data
        # ===================forward=====================
        output = model(x.to(device))
        loss = criterion(output, x.to(device))
        # ===================backward====================
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
    # ===================log========================
    print('epoch [{}/{}], loss:{:.4f}'
          .format(epoch + 1, num_epochs, loss.item()))


# 绘图
plt.title('Results')
plt.plot(np.arange(900, 1702, 2), np.transpose(torch.squeeze(x[1,:,:]).cpu().data.numpy()), '-r', label='Original Input')
plt.plot(np.arange(900, 1702, 2), np.transpose(torch.squeeze(output[1,:,:]).cpu().data.numpy()), '-b', label='Decoder Output')
plt.legend()
plt.show()

# 提取特征
model.eval()
with torch.no_grad():
    # feature extraction
    xtrain_features = model.encoder[0:4](xtrain.to(device))
    xtest_features = model.encoder[0:4](xtest.to(device))
    print(xtrain_features.size())
    print(xtest_features.size())

Xtrain_features = torch.squeeze(xtrain_features).cpu().data.numpy()
Xtest_features = torch.squeeze(xtest_features).cpu().data.numpy()
print(Xtrain_features.shape)
print(Xtest_features.shape)

# Create linear regression object
regr = LinearRegression()

# Train the model using the training sets
regr.fit(Xtrain_features, Y_train)

# Make predictions using the testing set
y_pred = regr.predict(Xtest_features)

Y_pred = mms.inverse_transform(y_pred.reshape(10, 1))

# The coefficients
print("Coefficients: \n", regr.coef_)
# The mean squared error
print("Mean squared error: %.2f" % mean_squared_error(Y_test, Y_pred))
# The coefficient of determination: 1 is perfect prediction
print("Coefficient of determination: %.2f" % r2_score(Y_test, Y_pred))

# Plot outputs
plt.scatter(Y_test, Y_pred, color="black")
plt.plot(Y_test, Y_test, color="blue", linewidth=3)

plt.xticks(())
plt.yticks(())

plt.show()