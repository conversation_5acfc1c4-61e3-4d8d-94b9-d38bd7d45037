import numpy as np
import scipy.io as sio
import random
from sklearn.model_selection import train_test_split
from sklearn import preprocessing
from sklearn.linear_model import LinearRegression
from sklearn.model_selection import cross_val_score
from deap import creator, base, tools, algorithms


def getFitness(individual, X, y):

    if individual.count(0) != len(individual):
        cols = [index for index in range(len(individual)) if individual[index] == 0]
        X_sub = np.delete(X, cols, axis=1)

        regr = LinearRegression()

        scores = cross_val_score(regr, X_sub, y, cv=3)
        Average_score = sum(scores)/len(scores)
        return (Average_score,)
    else:
        return(0,)


def geneticAlgorithm(X, y, n_population, n_generation):
    """
    Deap global variables
    Initialize variables to use eaSimple
    """
    # create individual
    creator.create("FitnessMax", base.Fitness, weights=(1.0,))
    creator.create("Individual", list, fitness=creator.FitnessMax)

    # create toolbox
    toolbox = base.Toolbox()
    toolbox.register("attr_bool", random.randint, 0, 1)
    toolbox.register("individual", tools.initRepeat,
                     creator.Individual, toolbox.attr_bool, X.shape[1])
    toolbox.register("population", tools.initRepeat, list,
                     toolbox.individual)
    toolbox.register("evaluate", getFitness, X=X, y=y)
    toolbox.register("mate", tools.cxOnePoint)
    toolbox.register("mutate", tools.mutFlipBit, indpb=0.05)
    toolbox.register("select", tools.selTournament, tournsize=3)

    # initialize parameters
    pop = toolbox.population(n=n_population)
    hof = tools.HallOfFame(n_population * n_generation)
    stats = tools.Statistics(lambda ind: ind.fitness.values)
    stats.register("avg", np.mean)
    stats.register("min", np.min)
    stats.register("max", np.max)

    # genetic algorithm
    pop, log = algorithms.eaSimple(pop, toolbox, cxpb=0.5, mutpb=0.2,
                                   ngen=n_generation, stats=stats, halloffame=hof,
                                   verbose=True)

    # return hall of fame
    return hof


def bestIndividual(hof, X, y):

    maxAccurcy = 0.0
    for individual in hof:
        # print('tuple value = ',individual.fitness.values)
        if(individual.fitness.values[0] > maxAccurcy):
            maxAccurcy = individual.fitness.values
            _individual = individual

    return _individual.fitness.values, _individual


if __name__ == '__main__':
    # 导入数据
    file = 'spectra_data.mat'
    data = sio.loadmat(file)
    X = data['NIR']
    Y = data['octane']
    print(X.shape)
    print(Y.shape)

    # 划分训练集/测试集
    X_train, X_test, Y_train, Y_test = train_test_split(X, Y, test_size=0.2)
    print(X_train.shape)
    print(X_test.shape)
    print(Y_train.shape)
    print(Y_test.shape)

    # 数据归一化
    mms = preprocessing.MinMaxScaler()
    X_train_mms = mms.fit_transform(X_train)
    X_test_mms = mms.transform(X_test)

    Y_train_mms = mms.fit_transform(Y_train)

    # get accuracy with all features
    individual = [1 for i in range(X_train_mms.shape[1])]
    print("Accuracy with all features: \t" + str(getFitness(individual, X_train_mms, Y_train_mms)) + "\n")

    # apply genetic algorithm
    n_pop = 20
    n_gen = 100
    hof = geneticAlgorithm(X_train_mms, Y_train_mms, n_pop, n_gen)

    # select the best individual
    accuracy, individual = bestIndividual(hof, X_train_mms, Y_train_mms)
    print('Best Accuracy: \t' + str(accuracy))
    print('Number of Features in Subset: \t' + str(individual.count(1)))
    print('Individual: \t\t' + str(individual))

    print('\n\ncreating a new classifier with the following selected features:')

    cols = [index for index in range(len(individual)) if individual[index] == 0]
    X_selected = np.delete(X_train_mms, cols, axis=1)
    selected_cols = [index for index in range(len(individual)) if individual[index] != 0]
    print(selected_cols)
    print(X_selected.shape)

    regr = LinearRegression()

    scores = cross_val_score(regr, X_selected, Y_train_mms, cv=3)
    Average_score = sum(scores)/len(scores)
    print("Accuracy with Feature Subset: \t" + str(Average_score) + "\n")
