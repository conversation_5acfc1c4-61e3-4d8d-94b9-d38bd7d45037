function VISSA=vissa(Xcal,ycal,nLV_max,fold,method,num_bms,flag)
% VISSA: Variable Iterative Space Shrinkage Approach
% Input: 
%        Xcal: The calibration data matrix of size m x n
%        ycal: The calibration response vector of size m x 1
%        nLV_max: The maximum number of latent variable
%        fold: The group number for cross validation
%        method: Pretreatment method
%        num_bms: The number of binray matrix sampling
%        flag: If flag=0,do not present the plots, if flag=1, present the plots

% Output:
%        VISSA: the result of VISSA
% Coded by:       
%        <PERSON><PERSON><PERSON>, April 3rd 2014
%        <EMAIL>
%        University of Bergen
% Revised in August 19th 2014
% Reference:  <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, L<PERSON><PERSON><PERSON>, A novel variable selection approach that iteratively optimizes variable space using weighted binary matrix sampling, Analyst, 139 (2014) 4836-4845.

%%        Initial settings
if nargin<7; flag=0;end;
if nargin<6; num_bms=1000;end;
if nargin<5; method=('center');end;
if nargin<4; fold=5;end;
if nargin<3; nLV_max=10;end

if num_bms>=2000 
    ratio=0.05;
else if num_bms<2000 && num_bms>=1000 
        ratio=0.1;
    else if num_bms<1000 && num_bms>=500
            ratio=0.2;
        else if num_bms<500
                fprintf('The number of BMS ought to be larger than 500')
                VISSA=[];
                return
            end
        end
    end
end

%%     The main program
% tic
[~,n]=size(Xcal);
num_selected_model=num_bms*ratio; %% the number of selected model
weight=ones(n,1)*0.5; %% set the initial weight for variables
W=[weight zeros(n,200)];
meanRMSECV=zeros(1,200);

for i=1:200
    %    generate the binary matrix for sampling
    binary_matrix=zeros(num_bms,n);
    for k=1:n
        column=[ones(round(weight(k)*num_bms),1);zeros(num_bms-round(weight(k)*num_bms),1)];
        column=column(randperm(num_bms));
        binary_matrix(:,k)=column;
    end
    
    %     if a row is full of zeros, set them to ones
    in= sum(binary_matrix,2)==0; % check the rows with all zeros
    binary_matrix(in,:)=1; % change the elements into 1
    
    %     caculate the RMSECV for all sub-models
    RMSECV=zeros(1,num_bms);
    for j=1:num_bms
    CV=plscvfold(Xcal(:,binary_matrix(j,:)==1),ycal,nLV_max,fold,method,0,0);
    RMSECV(j)=CV.RMSECV;
    end
    
    %     obtain new weights for sampling
    [RMSECV_sort, index]=sort(RMSECV);
    meanRMSECV(i)=mean(RMSECV_sort(1:num_selected_model));
    weight=sum(binary_matrix(index(1:num_selected_model),:))/num_selected_model;
    
    if flag==1
    figure(1)
    bar(weight)
    xlabel('varaible')
    ylabel('weight')
    axis([0 n 0 1])
    drawnow
    end
    
    W(:,i+1)=weight';
    if i>1 && meanRMSECV(i)>=meanRMSECV(i-1)
        break
    end
    fprintf('The %dth iteration finished.\n',i);
end
iteration=i-1;

if i<200
meanRMSECV(i:200)=[];
W(:,i+1:201)=[];
end


%% Obtain the sequence for selected variables
weight1=W==1;
diff_weight=zeros(n,iteration);
for k=1:iteration
    diff_weight(:,k)=weight1(:,k+1)-weight1(:,k);
end

informative_variables=[];
for j=1:iteration
    selected_new=find(diff_weight(:,j)==1);
    if length(selected_new)==1
        informative_variables=[informative_variables;selected_new];
    elseif length(selected_new)>=2
        [~,priority]=sort(W(selected_new,j),'descend');
        informative_variables=[informative_variables;selected_new(priority)];
    end
end 


weight0=W==0;
diff_weight=zeros(n,iteration);
for k=1:iteration
    diff_weight(:,k)=weight0(:,k+1)-weight0(:,k);
end
interfering_variables=[];
for j=1:iteration
    eliminated_new=find(diff_weight(:,j)==1);
    if length(eliminated_new)==1
        interfering_variables=[eliminated_new;interfering_variables];
    elseif length(eliminated_new)>=2
          [~,priority]=sort(W(eliminated_new,j),'descend');  
          einterfering_variables=[eliminated_new(priority);interfering_variables];
    end
end 

v=zeros(n,1);
v(informative_variables)=1;
v(interfering_variables)=1;

uninformative_variables=find(v==0);
[~,priority]=sort(W(uninformative_variables,iteration),'descend'); 

variables_in_sequence=[informative_variables;uninformative_variables(priority);interfering_variables];


% The final filtering
cv=zeros(1,n);
for i=1:n
    CV=plscvfold(Xcal(:,variables_in_sequence(1:i)),ycal,nLV_max,fold,method,0,0);
    cv(i)=CV.RMSECV;
end
nVAR=find(cv==min(cv));
RMSECV=min(cv);
    
    if flag==1
    figure(2)
    plot(cv)
    xlabel('varaible index')
    ylabel('RMSECV')
    end

% 
retained_variables=variables_in_sequence(1:nVAR);
variable_index=zeros(n,1);
for i=1:n
    variable_index(retained_variables)=1;
end

VISSA.iteration=iteration; % the number of interations
VISSA.minRMSECV=RMSECV; % the minimun RMSECV
VISSA.nVAR=nVAR; % the number of selected variables
VISSA.retained_variables=retained_variables; % the retained variables with decrease variable importance
VISSA.variable_index=variable_index; % the index of selected variables
VISSA.variable_in_sequence=variables_in_sequence; % all variables with decrease variable importance
VISSA.meanRMSECV=meanRMSECV; % the mean of RMSECV in each step
VISSA.weight=W; % the weights for variables
% VISSA.time=toc; 

%%%%%%%% ++++ Simple is good ! ++++  %%%%%%%%