function left_variables = backward_elimination(X,y,A,fold,method,varnumber)

%FINEEVALUATION Fine evaluation of candidate genes retained from earlier filtering.
% Inputs:
%+++ X: The data matrix of size m x p
%+++ y: The reponse vector of size m x 1
%+++ A: the maximal principle to extract.
%+++ fold: the group number for cross validation.
%+++ method : pretreatment method.
%
% Outputs:
%   left_variables : the variables that finally left.



CV=plscvfold(X,y,A,fold,method);
RMSECV0 = CV.RMSECV;
screenProcess=nan(size(X,2)-1,size(X,2)+2);

jj=1;
di=1:size(X,2);
index=1:size(X,2);
while size(X,2)>1
    parc=size(X,2);
    fprintf('Cross-validation with whole variables of this turn: %g\n',RMSECV0);
    screenProcess(jj,1)=RMSECV0;
    RMSECV_temp=nan(1,parc);    
    i=1;
    for k=1:parc
        cX=X;
        cX(:,k)=[];
        CV1=plscvfold(cX,y,A,fold,method,0);
        RMSECV_temp(k) = CV1.RMSECV;
%         fprintf('Cross-validation  without variables #%g: %g\n',index(k),RMSECV_temp(k));  
        if di(index(k))==0
            i=i+1;
        else
            %   screenProcess: the screening process of backward_elimination
            screenProcess(jj,di(index(k))+i)=RMSECV_temp(k);
        end
    end 
    [minRMSECV,minRMSECVIndex]=min(RMSECV_temp);
    jj=jj+1;
    if minRMSECV<=RMSECV0 
        fprintf('Variable #%g has been washed out, %d variables have been deleted.\n',varnumber(minRMSECVIndex),jj-1);
        screenProcess(jj-1,end)=index(minRMSECVIndex);
        di(index(minRMSECVIndex))=0;
        X(:,minRMSECVIndex)=[];
        index(minRMSECVIndex)=[];
        varnumber(minRMSECVIndex)=[];
        RMSECV0=minRMSECV;
    else
        disp('No any variable-deleting is necessary, screenning is finished.');
        disp(['Left variables: ',sprintf('%g,',varnumber)]);
        break
    end
end

left_variables=varnumber;



