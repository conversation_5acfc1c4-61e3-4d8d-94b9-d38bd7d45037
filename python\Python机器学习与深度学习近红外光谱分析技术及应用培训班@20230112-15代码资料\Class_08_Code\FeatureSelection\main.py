#!/usr/bin/env python 
# -*- coding:utf-8 -*-
__author__ = '<PERSON><PERSON> @ Shanxi University'

import matplotlib.pyplot as plt
import numpy as np
import scipy.io as sio
from sklearn import preprocessing
from sklearn.tree import DecisionTreeRegressor
from sklearn.metrics import mean_squared_error, r2_score

# 导入数据集
file = 'spectra_data.mat'
data = sio.loadmat(file)
X = data['NIR']
Y = data['octane']
print(X.shape)
print(Y.shape)

# 划分训练集、验证集与测试集
k = np.random.permutation(X.shape[0])
print(k)
# 训练集
X_train = X[k[:50], :]
Y_train = Y[k[:50], :]
# 测试集
X_test = X[k[50:], :]
Y_test = Y[k[50:], :]

# 归一化
mms = preprocessing.MinMaxScaler()
X_train = mms.fit_transform(X_train) # [50, 100] ---> [0, 1]

# mms = preprocessing.MinMaxScaler()
# X_test = mms.fit_transform(X_test)  # [20, 120]  ---> [0, 1]

X_test = mms.transform(X_test)

Y_train = mms.fit_transform(Y_train)

print(X_train.shape)
print(Y_train.shape)
print(X_test.shape)
print(Y_test.shape)

# 训练模型
dtree = DecisionTreeRegressor()
dtree.fit(X_train, Y_train)

# 预测
y_pred = dtree.predict(X_test)
Y_pred = mms.inverse_transform(y_pred.reshape(10, 1))

# The mean squared error
print("Mean squared error: %.2f" % mean_squared_error(Y_test, Y_pred))
# The coefficient of determination: 1 is perfect prediction
print("Coefficient of determination: %.2f" % r2_score(Y_test, Y_pred))

# 特征重要度
features_names = []
for i in range(1, 402):
    name = 'x' + str(i)
    features_names.append(name)
features = np.array(features_names)

# 被选中的特征并降序排列
importances = dtree.feature_importances_
index = importances > 0
selected_features = features[index]
selected_importance = importances[index]
indices = np.argsort(selected_importance)[::-1]

num_features = len(selected_features)

# 将特征重要度以柱状图展示
plt.figure()
plt.title("Feature importances")
plt.bar(range(num_features), selected_importance[indices], color="g", align="center")
plt.xticks(range(num_features), selected_features)
plt.xlim([-1, num_features])
plt.xlabel("Selected features")
plt.ylabel("Importance")
plt.show()

# 输出各个特征的重要度
for i in range(num_features):
    print("{0} -> {1:.3f}".format(selected_features[i], selected_importance[indices[i]]))