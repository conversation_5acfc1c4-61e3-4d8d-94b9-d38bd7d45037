#!/usr/bin/env python 
# -*- coding:utf-8 -*-
__author__ = '<PERSON><PERSON> @ Shanxi University'

import matplotlib.pyplot as plt
import numpy as np

# region 简单图形绘制
# 1.根据坐标点绘制
x = np.array([1, 2, 3, 4, 5, 6, 7, 8])
y = np.array([3, 5, 7, 6, 2, 6, 10, 15])
plt.plot(x, y, 'r')
z = np.array([13, 25, 17, 36, 21, 16, 10, 15])
plt.bar(x, z, 0.5, alpha=0.5, color='b')
# plt.savefig('test.png', dpi=200)
plt.show()


# 2. 根据函数图像绘制
x = np.linspace(0, 2*np.pi, 256, endpoint=True)
y = np.sin(x)
z = np.cos(x)
plt.plot(x, y)
plt.plot(x, z)

plt.xlabel('x')
plt.ylabel('y')
plt.title(u'三角函数曲线', fontproperties='SimHei', fontsize=14)
plt.show()

# 3. figure的使用
x = np.linspace(0, 2*np.pi, 256, endpoint=True)
y = np.sin(x)
z = np.cos(x)
plt.figure(num = 5, figsize = (8, 8))
plt.plot(x, y)
plt.plot(x, z, color = 'red', linewidth = 1.0, linestyle = '--')
plt.show()
# endregion

# region 设置坐标轴
x = np.linspace(0, 2*np.pi, 256, endpoint=True)
y = np.sin(x)
z = np.cos(x)
plt.figure()
plt.plot(x, y)
plt.plot(x, z, color = 'red', linewidth = 1.0, linestyle = '--')

plt.xlim((0, 2*np.pi))      # 设置坐标轴的取值范围
plt.ylim((-1.2, 1.2))

plt.xlabel(u'x轴', fontproperties='SimHei', fontsize=14)     # 设置坐标轴的Label
plt.ylabel(u'y轴', fontproperties='SimHei', fontsize=14)

plt.xticks(np.linspace(0, 2*np.pi, 5))       # 设置坐标轴的刻度

plt.grid(True)      # 显示网格线
# plt.grid(False)
#
ax = plt.gca()      # 获取当前的坐标轴 Get Current Axes
ax.spines['right'].set_color('none')    # 不显示右边框和上边框
ax.spines['top'].set_color('none')

plt.show()
# endregion

# region 设置legend图例
x = np.linspace(0, 2*np.pi, 256, endpoint=True)
y = np.sin(x)
z = np.cos(x)

plt.figure()
plt.plot(x, y, '-r', label='sin(x)')
plt.plot(x, z, ':b', label='cos(x)')

plt.legend()    # 添加图例

plt.show()

# endregion

# region 添加注解
x = np.linspace(0, 2*np.pi, 256, endpoint=True)
y = np.sin(x)
z = np.cos(x)

plt.figure()
plt.plot(x, y, '-r', label='sin(x)')
plt.plot(x, z, ':b', label='cos(x)')

plt.xlim((0, 2*np.pi))      # 设置坐标轴的取值范围
plt.ylim((-1.2, 1.2))

plt.legend()    # 添加图例

x0 = np.pi/4
y0 = np.sqrt(2)/2
plt.scatter(x0, y0, s=50, color='blue') #绘制点(pi/4, sqrt(2)/2)
plt.plot([x0, x0], [y0, -1.2], 'k--', linewidth=3) # 绘制虚线

plt.annotate(r'$sin(\pi/4) = cos(\pi/4)$',xy=(x0, y0), xycoords='data', xytext=(+20, -20),\
             textcoords='offset points', fontsize=8, arrowprops=dict(arrowstyle = '->', connectionstyle='arc3, rad=.2'))

plt.show()
# endregion

# region 绘制散点图
x = np.random.normal(0, 1, 100)   # 均值为0，方差为1的随机数
y = np.random.normal(0, 1, 100)

color = np.arctan2(y, x)    # 计算随机数

plt.scatter(x, y, s = 50, c = color, alpha = 0.5) # 绘制散点图

plt.xlim((-1.5, 1.5))   # 设置坐标轴范围
plt.ylim((-1.5, 1.5))

plt.xticks(())  # 不显示坐标轴的值
plt.yticks(())

plt.show()
# endregion

# region 绘制柱状图
X = np.arange(10)

y1 = np.random.uniform(0.5, 1.0, 10)    # 均匀分布(0.5, 1.0)之间
y2 = np.random.uniform(0.5, 1.0, 10)

plt.bar(X, y1, facecolor='blue', edgecolor='white')     # 绘制柱状图, 向上
plt.bar(X, -y2, facecolor='green', edgecolor='white')   # 绘制柱状图, 向下

# 在柱状图上显示具体数值, ha水平对齐, va垂直对齐
for x, y in zip(X, y1):
    plt.text(x + 0.05, y + 0.1, '%.2f' % y, ha='center', va='bottom')

for x, y in zip(X, y2):
    plt.text(x + 0.05, -y - 0.2, '%.2f' % y, ha='center', va='bottom')

plt.xlim(-1, 10) # 设置坐标轴范围
plt.ylim(-1.5, 1.5)

plt.xticks(())  # 去除坐标轴
plt.yticks(())

plt.show()
# endregion

# region 绘制等高线图


def f(x, y):    # 定义等高线高度函数
    return (1 - x / 2 + x ** 5 + y ** 3) * np.exp(- x ** 2 - y ** 2)


x = np.linspace(-3, 3, 256)
y = np.linspace(-3, 3, 256)
X, Y = np.meshgrid(x, y)    # 生成网络数据

# 填充等高线的颜色, 8是等高线分为几部分
plt.contourf(X, Y, f(X, Y), 16, alpha=0.75, cmap=plt.cm.hot)
# 绘制等高线
C = plt.contour(X, Y, f(X, Y), 16, colors='black', linewidth=0.5)
# 绘制等高线数据
plt.clabel(C, inline=True, fontsize=10)

plt.xticks(())  # 去除坐标轴
plt.yticks(())

plt.show()
# endregion


# region 绘制3D图形
from mpl_toolkits.mplot3d import Axes3D

fig = plt.figure()  # 定义figure
ax = Axes3D(fig)    # 将figure变为3d

x = np.arange(-4, 4, 0.25)
y = np.arange(-4, 4, 0.25)
X, Y = np.meshgrid(x, y)        # 生成网格数据
R = np.sqrt(X ** 2 + Y ** 2)    # 计算每个点对的长度

Z = np.sin(R)   # 计算Z轴的高度

# 绘制3D曲面
ax.plot_surface(X, Y, Z, rstride=1, cstride=1,
                cmap=plt.get_cmap('rainbow'))

# 绘制从3D曲面到底部的投影
ax.contour(X, Y, Z, zdim='z', offset=-2, cmap='rainbow')

# 设置z轴的维度
ax.set_zlim(-2, 2)

plt.show()
# endregion

# region 绘制多图
x = np.linspace(0, 2*np.pi, 256, endpoint=True)
y = np.sin(x)
z = np.cos(x)

plt.figure()
plt.subplot(2, 2, 1)    # 绘制第一个图
plt.plot(x, y)

plt.subplot(2, 2, 2)    # 绘制第二个图
plt.plot(x, z)

plt.subplot(2, 2, 3)    # 绘制第三个图
plt.plot(x, x**x)

plt.subplot(2, 2, 4)    # 绘制第四个图
plt.plot(x, np.log(x))

plt.show()

# 不规则划分子窗口例子1
plt.figure()
plt.subplot(2, 1, 1)
plt.plot([0, 1], [0, 1])

plt.subplot(2, 3, 4)
plt.plot([0, 1], [0, 1])

plt.subplot(2, 3, 5)
plt.plot([0, 1], [0, 1])

plt.subplot(2, 3, 6)
plt.plot([0, 1], [0, 1])

plt.show()

# 不规则划分子窗口例子2
# plt.figure()
# ax1 = plt.subplot2grid((3, 3), (0, 0), colspan=3, rowspan=1)
# ax1.plot([0, 1], [0, 1])
# ax1.set_title('Test')
#
# ax2 = plt.subplot2grid((3, 3), (1, 0), colspan=2, rowspan=1)
# ax2.plot([0, 1], [0, 1])
#
# ax3 = plt.subplot2grid((3, 3), (1, 2), colspan=1, rowspan=1)
# ax3.plot([0, 1], [0, 1])
#
# ax4 = plt.subplot2grid((3, 3), (2, 0), colspan=3, rowspan=1)
# ax4.plot([0, 1], [0, 1])
#
# plt.show()

# 不规则划分子窗口例子3
# import matplotlib.gridspec as gridspec
#
# plt.figure()
#
# gs = gridspec.GridSpec(3, 3)
# ax1 = plt.subplot(gs[0, :])
# ax2 = plt.subplot(gs[1, 0:2])
# ax3 = plt.subplot(gs[1, 2])
# ax4 = plt.subplot(gs[2, :])
#
# ax1.plot([0, 1], [0, 1])
# ax1.set_title('Test')
#
# ax2.plot([0, 1], [0, 1])
#
# ax3.plot([0, 1], [0, 1])
#
# ax4.plot([0, 1], [0, 1])
#
# plt.show()
# endregion

# region 图的嵌套
fig = plt.figure()

x = [1, 2, 3, 4, 5, 6, 7]
y = [1, 3, 4, 2, 5, 8, 6]

# figure的百分比, 从figure 10%的位置开始绘制, 宽高是figure的80%
left, bottom, width, height = 0.1, 0.1, 0.8, 0.8
# 获得绘制的句柄
ax1 = fig.add_axes([left, bottom, width, height])
# 绘制点(x,y)
ax1.plot(x, y, 'r')
ax1.set_xlabel('x')
ax1.set_ylabel('y')
ax1.set_title('test')


# 嵌套方法一
# figure的百分比, 从figure 10%的位置开始绘制, 宽高是figure的80%
left, bottom, width, height = 0.2, 0.6, 0.25, 0.25
# 获得绘制的句柄
ax2 = fig.add_axes([left, bottom, width, height])
# 绘制点(x,y)
ax2.plot(x, y, 'r')
ax2.set_xlabel('x')
ax2.set_ylabel('y')
ax2.set_title('part1')

# 嵌套方法二
plt.axes([bottom, left, width, height])
plt.plot(x, y, 'r')
plt.xlabel('x')
plt.ylabel('y')
plt.title('part2')

plt.show()
# endregion


