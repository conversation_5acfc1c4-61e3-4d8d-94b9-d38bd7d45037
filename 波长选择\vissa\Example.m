%% 清空
clc
clear

%% 导入数据 运行前先把数据格式调整，第一行为全波长点，行为样品，列为波长反射率，最后一列为理化值y
Data = xlsread('小麦248.xlsx');
data = Data(2:end,:);  %光谱阵和理化值
nm = Data(1,1:end-1);  %读取波长数据，确保维度匹配
X = data(:,1:end-1);    %光谱阵
y = data(:,end);       %理化值

%% VISSA
%参数设置
nLV_max = 10;
fold = 5;
method = 'center';
num_bms = 1000;
flag = 0;

Xcal = X;
ycal = y;
%波长选择
VISSA=vissa(Xcal,ycal,10,5,'center',1000,1);
SelectedVariables = VISSA.retained_variables; %筛选的特征波长索引
nm_sel = nm(SelectedVariables);%筛选的特征波长

%% 绘图
figure,hold,grid off;
plot(nm,X(1,:),'color', '#2878b5','LineWidth', 1);
plot(nm_sel,X(1,SelectedVariables),'ko', 'MarkerSize', 4, 'MarkerFaceColor', 'r')
xlim([730,1100]);
set(gca,'FontSize',12,'Fontname', 'Times New Roman','LineWidth',1,'FontWeight','bold'); 
xlabel('Wavelength (nm)');
ylabel('Reflectance');
box on;

%% 特征波长数据集
%X = X(:,SelectedVariables);

%% 训练集交叉验证
CV=plscvfold(Xcal,ycal,10,5,'center',0,0);
rmsecv1=CV.RMSECV; % The RMSECV obtained on full spectrum
[rmsep1,rmsec1]=predict(Xcal,ycal,Xtest,ytest,1:100,CV.optPC,'center'); % The RMSEC and RMSEP obtained on full spectrum
CV=plscvfold(Xcal(:,VISSA.variable_index==1),ycal,10,5,'center',0,0);
rmsecv2=CV.RMSECV; % The RMSECV obtained after variable selection
[rmsep2,rmsec2]=predict(Xcal,ycal,Xtest,ytest,VISSA.variable_index==1,CV.optPC,'center');  % The RMSEC and RMSEP obtained after variable selection

% The evolution of weights during iteration
figure
plot(VISSA.weight')
ylabel('Weight')
xlabel('Iteration')


