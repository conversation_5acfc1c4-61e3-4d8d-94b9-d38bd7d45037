#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
生成测试数据文件，用于测试数据导入功能
"""

import numpy as np
import pandas as pd
import scipy.io as sio
import os

def generate_test_data():
    """生成测试数据"""
    # 设置随机种子
    np.random.seed(42)
    
    # 生成模拟光谱数据
    n_samples = 50
    n_wavelengths = 200
    wavelengths = np.linspace(400, 2500, n_wavelengths)  # 400-2500nm
    
    # 生成基础光谱模式
    base_spectrum = np.exp(-((wavelengths - 1200) / 300) ** 2)  # 高斯峰
    base_spectrum += 0.5 * np.exp(-((wavelengths - 1800) / 200) ** 2)  # 第二个峰
    
    # 生成样本光谱（添加噪声和变化）
    X_data = []
    y_data = []
    
    for i in range(n_samples):
        # 添加随机变化
        scale = 0.8 + 0.4 * np.random.random()
        shift = np.random.normal(0, 50)
        noise = np.random.normal(0, 0.05, n_wavelengths)
        
        # 生成光谱
        spectrum = scale * np.interp(wavelengths + shift, wavelengths, base_spectrum) + noise
        spectrum += 0.1 * np.random.random()  # 基线偏移
        
        X_data.append(spectrum)
        
        # 生成对应的y值（模拟某种化学成分含量）
        y_value = 10 + 5 * scale + 0.1 * shift + np.random.normal(0, 0.5)
        y_data.append(y_value)
    
    X_data = np.array(X_data)
    y_data = np.array(y_data)
    
    return X_data, y_data, wavelengths

def save_test_files():
    """保存测试文件"""
    X_data, y_data, wavelengths = generate_test_data()
    
    # 创建测试数据目录
    test_dir = "test_data"
    if not os.path.exists(test_dir):
        os.makedirs(test_dir)
    
    # 1. 保存为CSV格式（分离的X和y）
    print("生成CSV格式测试文件...")
    
    # X数据
    X_df = pd.DataFrame(X_data, columns=[f"{w:.1f}" for w in wavelengths])
    X_df.to_csv(f"{test_dir}/test_X_data.csv", index=False)
    
    # y数据
    y_df = pd.DataFrame(y_data, columns=['concentration'])
    y_df.to_csv(f"{test_dir}/test_y_data.csv", index=False)
    
    # 2. 保存为CSV格式（合并数据）
    combined_df = X_df.copy()
    combined_df['y'] = y_data
    combined_df.to_csv(f"{test_dir}/test_combined_data.csv", index=False)
    
    # 3. 保存为MAT格式
    print("生成MAT格式测试文件...")
    sio.savemat(f"{test_dir}/test_spectral_data.mat", {
        'NIR': X_data,
        'octane': y_data.reshape(-1, 1),
        'wavelengths': wavelengths
    })
    
    # 4. 保存为Excel格式
    print("生成Excel格式测试文件...")
    with pd.ExcelWriter(f"{test_dir}/test_spectral_data.xlsx") as writer:
        X_df.to_excel(writer, sheet_name='X_data', index=False)
        y_df.to_excel(writer, sheet_name='y_data', index=False)
        combined_df.to_excel(writer, sheet_name='combined_data', index=False)
    
    # 5. 生成数据说明文件
    with open(f"{test_dir}/README.txt", "w", encoding="utf-8") as f:
        f.write("SpectraTool 测试数据说明\n")
        f.write("=" * 30 + "\n\n")
        f.write(f"数据概述:\n")
        f.write(f"- 样本数量: {X_data.shape[0]}\n")
        f.write(f"- 波长点数: {X_data.shape[1]}\n")
        f.write(f"- 波长范围: {wavelengths[0]:.1f} - {wavelengths[-1]:.1f} nm\n")
        f.write(f"- y值范围: {y_data.min():.2f} - {y_data.max():.2f}\n\n")
        f.write("文件说明:\n")
        f.write("- test_X_data.csv: 光谱数据（X矩阵）\n")
        f.write("- test_y_data.csv: 标签数据（y向量）\n")
        f.write("- test_combined_data.csv: 合并数据（X+y）\n")
        f.write("- test_spectral_data.mat: MATLAB格式数据\n")
        f.write("- test_spectral_data.xlsx: Excel格式数据\n\n")
        f.write("使用方法:\n")
        f.write("1. 在SpectraTool中选择'数据导入'模块\n")
        f.write("2. 点击相应的导入按钮\n")
        f.write("3. 选择对应的测试文件\n")
        f.write("4. 查看数据预览和统计信息\n")
    
    print(f"\n✅ 测试数据生成完成！")
    print(f"📁 文件保存位置: {os.path.abspath(test_dir)}")
    print(f"📊 数据规模: {X_data.shape[0]} 样本 × {X_data.shape[1]} 波长点")
    print(f"🎯 y值范围: {y_data.min():.2f} - {y_data.max():.2f}")
    
    return test_dir

if __name__ == "__main__":
    save_test_files()
