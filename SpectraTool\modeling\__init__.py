from .classification.resnet_transfer_model import ResNetTransferModel
from .regression.autoencoder_model import AutoEncoderModel
from .classification.transfer_mlp_model import TransferMLPModel

from .regression.ridge_model import RidgeModel
from .regression.pls_model import PLSModel
from .regression.svr_model import SVRModel
from .regression.xgb_regressor_model import XGBRegressorModel
from .regression.lgbm_regressor_model import LGBMRegressorModel

from .classification.logistic_model import LogisticRegressionModel
from .classification.svm_model import SVMClassifierModel
from .classification.lgbm_model import LGBMClassifierModel

from .regression.lasso_model import LassoModel
from .regression.elasticnet_model import ElasticNetModel
from .classification.random_forest_model import RandomForestModel
from .classification.xgb_model import XGBModel

MODEL_REGISTRY = {
    "ResNet Transfer": ResNetTransferModel,
    "AutoEncoder": AutoEncoderModel,
    "Transfer MLP": TransferMLPModel,

    "Lasso": LassoModel,
    "ElasticNet": ElasticNetModel,
    "Random Forest": RandomForestModel,
    "XGBoost": XGBModel
}
