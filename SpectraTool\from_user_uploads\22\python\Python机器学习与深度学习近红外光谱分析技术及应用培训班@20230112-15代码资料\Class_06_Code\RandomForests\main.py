#!/usr/bin/env python 
# -*- coding:utf-8 -*-
__author__ = '<PERSON><PERSON> @ Shanxi University'

from sklearn.ensemble import RandomForestRegressor
from sklearn import preprocessing
import numpy as np
import scipy.io as sio
from sklearn.model_selection import train_test_split
import matplotlib.pylab as plt
from sklearn.metrics import mean_squared_error
from sklearn.metrics import r2_score

# 导入数据
file = 'spectra_data.mat'
data = sio.loadmat(file)
X = data['NIR']
Y = data['octane']
print(X.shape)
print(Y.shape)

# 划分训练集/测试集
X_train, X_test, Y_train, Y_test = train_test_split(X, Y, test_size=0.2)
print(X_train.shape)
print(X_test.shape)
print(Y_train.shape)
print(Y_test.shape)

# 数据归一化
mms = preprocessing.MinMaxScaler()
X_train_mms = mms.fit_transform(X_train)
X_test_mms = mms.transform(X_test)

Y_train_mms = mms.fit_transform(Y_train)

# 建立随机森林模型
trees_num = 200
forest = RandomForestRegressor(n_estimators=trees_num)

# 训练随机森林模型
forest.fit(X_train_mms, Y_train_mms.ravel())

# 决策树模型预测
y_sim = forest.predict(X_test_mms)

# 反归一化
Y_sim = mms.inverse_transform(y_sim.reshape(Y_test.shape[0], -1))

# 结果分析
MSE = mean_squared_error(Y_test, Y_sim)
print('MSE = ', MSE)

R2 = r2_score(Y_test, Y_sim)
print('R2 = ', R2)

# 绘图
plt.figure()
plt.rcParams['font.sans-serif']=['SimHei']
plt.rcParams['axes.unicode_minus'] = False

plt.scatter(np.arange(1,Y_test.shape[0]+1), Y_test, s=20, edgecolor="black",
            c="darkorange", label="True")
plt.plot(np.arange(1,Y_test.shape[0]+1), Y_sim, color="cornflowerblue",
         label="max_depth=2", linewidth=2)

plt.xticks(np.arange(0, Y_test.shape[0]+1, 3))
plt.xlabel("样本编号")
plt.ylabel("预测值")
plt.title("随机森林回归模型")
plt.show()


