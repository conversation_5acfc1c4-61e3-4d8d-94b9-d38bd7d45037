function [RMS<PERSON>,RMSEF]=predict(Xtrain,ytrain,Xtest,ytest,selected_variables,optPC,method)

%++ prediction with the selected variables

if nargin<7;method='center';end;
if nargin<6;optPC=10;end;


Xtrain=Xtrain(:,selected_variables);
Xtest=Xtest(:,selected_variables);
PLS=pls(Xtrain,ytrain,optPC,method);
Xtest_expand=[Xtest ones(size(Xtest,1),1)];
coef=PLS.regcoef_original;
ypred=Xtest_expand*coef(:,end);
%%%%%%%%%   B E CAUTIOUS  ####################   
RMSEF=sqrt(PLS.SSE/size(Xtrain,1));
RMSEP=sqrt(sum((ytest-ypred).^2)/size(Xtest,1));
