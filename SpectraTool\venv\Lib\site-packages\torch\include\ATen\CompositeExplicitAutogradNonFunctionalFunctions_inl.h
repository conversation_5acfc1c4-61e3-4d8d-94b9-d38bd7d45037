#pragma once
// @generated by torchgen/gen.py from DispatchKeyFunctions_inl.h

// NB: The implementing C++ file is RegisterDispatchKey.cpp

// The only #includes we need are for custom classes that have defaults in the C++ API
#include <c10/core/MemoryFormat.h>
#include <c10/core/Scalar.h>
#include <ATen/core/Reduction.h>

#if defined(AT_PER_OPERATOR_HEADERS) && defined(TORCH_ASSERT_ONLY_METHOD_OPERATORS)
#error This change adds a dependency on all pytorch operators, meaning the     \
  file will need to be re-compiled every time an operator is changed or added. \
  Consider including a specific operator from                                  \
  <ATen/ops/{my_operator}_compositeexplicitautogradnonfunctional_dispatch.h>.                   \
  See NOTE [TORCH_ASSERT_ONLY_METHOD_OPERATORS].
#endif

#include <ATen/ops/_addmm_activation_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_conj_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_convert_indices_from_coo_to_csr_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_convert_indices_from_csr_to_coo_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_fw_primal_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_indices_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_linalg_det_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_linalg_eigh_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_linalg_slogdet_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_linalg_solve_ex_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_linalg_svd_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_log_softmax_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_log_softmax_backward_data_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_make_dual_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_neg_view_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_nested_get_values_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_nested_view_from_buffer_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_nested_view_from_jagged_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_reshape_alias_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_softmax_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_softmax_backward_data_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_sparse_broadcast_to_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_test_autograd_multiple_dispatch_view_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_trilinear_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_upsample_bicubic2d_aa_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_upsample_bicubic2d_aa_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_upsample_bilinear2d_aa_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_upsample_bilinear2d_aa_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_upsample_nearest_exact1d_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_upsample_nearest_exact1d_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_upsample_nearest_exact2d_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_upsample_nearest_exact2d_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_upsample_nearest_exact3d_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_upsample_nearest_exact3d_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/_values_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/acos_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/acosh_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/adaptive_max_pool2d_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/adaptive_max_pool2d_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/adaptive_max_pool3d_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/adaptive_max_pool3d_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/add_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/addcdiv_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/addcmul_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/addmm_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/addmv_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/alias_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/all_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/amax_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/amin_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/aminmax_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/any_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/argmax_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/argmin_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/as_strided_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/as_strided_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/as_strided_scatter_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/asin_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/asinh_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/atan_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/atan2_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/atanh_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/avg_pool2d_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/avg_pool2d_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/avg_pool3d_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/avg_pool3d_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/baddbmm_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/bernoulli_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/bitwise_and_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/bitwise_left_shift_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/bitwise_not_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/bitwise_or_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/bitwise_right_shift_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/bitwise_xor_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/bmm_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/cat_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/ccol_indices_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/ceil_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/clamp_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/clamp_max_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/clamp_min_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/col_indices_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/copysign_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/cos_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/cosh_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/crow_indices_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/cumprod_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/cumsum_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/detach_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/diag_embed_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/diagonal_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/diagonal_scatter_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/digamma_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/div_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/elu_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/elu_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/eq_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/erf_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/erfc_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/erfinv_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/exp_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/exp2_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/expand_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/expm1_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/floor_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/fmax_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/fmin_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/fmod_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/frac_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/fractional_max_pool2d_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/fractional_max_pool2d_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/fractional_max_pool3d_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/gather_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/gcd_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/ge_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/gelu_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/gelu_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/glu_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/gt_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/hardshrink_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/hardshrink_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/hardsigmoid_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/hardsigmoid_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/heaviside_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/hypot_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/i0_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/igamma_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/igammac_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/index_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/index_add_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/index_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/index_reduce_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/indices_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/isin_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/isneginf_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/isposinf_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/lcm_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/le_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/leaky_relu_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/leaky_relu_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/lerp_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/lgamma_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/lift_fresh_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/linalg_cholesky_ex_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/linalg_cross_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/linalg_inv_ex_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/linalg_ldl_factor_ex_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/linalg_ldl_solve_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/linalg_lu_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/linalg_lu_factor_ex_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/linalg_lu_solve_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/linalg_pinv_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/linalg_qr_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/linalg_vector_norm_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/log_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/log10_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/log1p_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/log2_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/logaddexp_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/logaddexp2_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/logit_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/logsumexp_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/lt_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/lu_unpack_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/max_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/max_pool2d_with_indices_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/max_pool2d_with_indices_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/maximum_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/mean_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/min_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/minimum_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/mish_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/mm_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/mse_loss_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/mul_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/narrow_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/ne_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/neg_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/new_empty_strided_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/nextafter_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/nll_loss_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/nll_loss_forward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/norm_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/permute_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/pixel_shuffle_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/pixel_unshuffle_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/polygamma_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/pow_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/prod_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/reciprocal_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/reflection_pad1d_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/reflection_pad1d_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/reflection_pad3d_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/reflection_pad3d_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/remainder_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/renorm_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/replication_pad1d_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/replication_pad1d_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/replication_pad2d_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/replication_pad3d_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/round_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/row_indices_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/rsqrt_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/scatter_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/scatter_add_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/scatter_reduce_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/select_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/select_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/select_scatter_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/sgn_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/sigmoid_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/sigmoid_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/sign_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/signbit_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/silu_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/silu_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/sin_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/sinc_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/sinh_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/slice_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/slice_scatter_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/slow_conv_transpose2d_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/smooth_l1_loss_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/softplus_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/softplus_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/softshrink_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/softshrink_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/sort_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_airy_ai_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_bessel_j0_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_bessel_j1_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_bessel_y0_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_bessel_y1_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_chebyshev_polynomial_t_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_chebyshev_polynomial_u_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_chebyshev_polynomial_v_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_chebyshev_polynomial_w_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_entr_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_erfcx_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_hermite_polynomial_h_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_hermite_polynomial_he_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_i0e_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_i1_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_i1e_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_laguerre_polynomial_l_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_legendre_polynomial_p_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_log_ndtr_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_modified_bessel_i0_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_modified_bessel_i1_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_modified_bessel_k0_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_modified_bessel_k1_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_ndtri_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_scaled_modified_bessel_k0_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_scaled_modified_bessel_k1_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_shifted_chebyshev_polynomial_t_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_shifted_chebyshev_polynomial_u_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_shifted_chebyshev_polynomial_v_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_shifted_chebyshev_polynomial_w_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_spherical_bessel_j0_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_xlog1py_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/special_zeta_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/split_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/split_with_sizes_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/sqrt_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/squeeze_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/sub_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/sum_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/t_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/tan_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/tanh_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/tanh_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/threshold_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/threshold_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/topk_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/transpose_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/triangular_solve_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/tril_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/triu_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/trunc_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/unbind_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/unfold_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/unsqueeze_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/upsample_bicubic2d_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/upsample_bicubic2d_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/upsample_bilinear2d_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/upsample_bilinear2d_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/upsample_linear1d_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/upsample_linear1d_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/upsample_nearest1d_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/upsample_nearest1d_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/upsample_nearest2d_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/upsample_nearest2d_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/upsample_nearest3d_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/upsample_nearest3d_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/upsample_trilinear3d_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/upsample_trilinear3d_backward_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/values_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/view_as_complex_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/view_as_real_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/view_copy_compositeexplicitautogradnonfunctional_dispatch.h>
#include <ATen/ops/xlogy_compositeexplicitautogradnonfunctional_dispatch.h>



