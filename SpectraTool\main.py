import sys
from PyQt5.QtWidgets import QApplication, QSplashScreen
from PyQt5.QtGui import QPixmap, QIcon
from PyQt5.QtCore import Qt, QTimer
from gui.main_ui import MainWindow

def main():
    app = QApplication(sys.argv)

    # 设置图标
    app.setWindowIcon(QIcon("assets/icon.ico"))

    # 启动闪屏
    splash = QSplashScreen(QPixmap("assets/splash.png"))
    splash.setWindowFlags(Qt.WindowStaysOnTopHint | Qt.FramelessWindowHint)
    splash.show()
    QTimer.singleShot(1500, splash.close)  # 1.5 秒后关闭

    # 加载主界面
    window = MainWindow()
    window.show()

    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
