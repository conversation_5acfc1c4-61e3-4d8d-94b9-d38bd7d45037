import numpy as np
from scipy.signal import savgol_filter

def savgol(input_data, window_length=11, polyorder=2, deriv=0):
    """
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 滤波平滑/导数
    参数:
        input_data: 2D numpy array (samples x variables)
        window_length: 滤波窗口长度（奇数）
        polyorder: 多项式阶数
        deriv: 导数阶数（0=平滑, 1=一阶导, 2=二阶导）
    返回:
        filtered_data: 处理后数据
    """
    return savgol_filter(input_data, window_length, polyorder, deriv=deriv, axis=1)
