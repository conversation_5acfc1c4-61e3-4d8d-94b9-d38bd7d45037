from PyQt5.QtWidgets import QWidget, QVBoxLayout, QPushButton, QFileDialog, QLabel
import joblib
import pandas as pd
import numpy as np
import os

class ExportTab(QWidget):
    def __init__(self):
        super().__init__()
        self.model = None
        self.predictions = None
        self.model_loaded = False

        layout = QVBoxLayout()

        self.model_path = ""
        self.pred_path = ""

        load_model_btn = QPushButton("加载已训练模型 (.pkl/.joblib)")
        load_model_btn.clicked.connect(self.load_model)

        save_model_btn = QPushButton("保存当前模型为 .joblib")
        save_model_btn.clicked.connect(self.save_model)

        load_pred_btn = QPushButton("加载预测结果数据 (.csv)")
        load_pred_btn.clicked.connect(self.load_prediction)

        save_pred_btn = QPushButton("导出预测结果为 .csv")
        save_pred_btn.clicked.connect(self.save_prediction)

        self.status_label = QLabel("未加载模型或预测数据")

        layout.addWidget(load_model_btn)
        layout.addWidget(save_model_btn)
        layout.addWidget(load_pred_btn)
        layout.addWidget(save_pred_btn)
        layout.addWidget(self.status_label)
        self.setLayout(layout)

    def load_model(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "选择模型文件", "", "Model Files (*.pkl *.joblib)")
        if file_path:
            self.model = joblib.load(file_path)
            self.model_loaded = True
            self.status_label.setText(f"模型已加载：{os.path.basename(file_path)}")

    def save_model(self):
        if self.model is None:
            self.status_label.setText("当前无模型可保存")
            return
        path, _ = QFileDialog.getSaveFileName(self, "保存模型为", "model.joblib", "Model Files (*.joblib)")
        if path:
            joblib.dump(self.model, path)
            self.status_label.setText(f"模型已保存到：{path}")

    def load_prediction(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "选择预测结果 CSV", "", "CSV Files (*.csv)")
        if file_path:
            self.predictions = pd.read_csv(file_path)
            self.status_label.setText(f"预测结果已加载：{os.path.basename(file_path)}")

    def save_prediction(self):
        if self.predictions is None:
            self.status_label.setText("当前无预测结果可导出")
            return
        path, _ = QFileDialog.getSaveFileName(self, "保存预测结果为", "predictions.csv", "CSV Files (*.csv)")
        if path:
            self.predictions.to_csv(path, index=False)
            self.status_label.setText(f"预测结果已导出到：{path}")
