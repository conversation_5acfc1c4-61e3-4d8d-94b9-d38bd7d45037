#!/usr/bin/env python 
# -*- coding:utf-8 -*-
__author__ = '<PERSON><PERSON> @ Shanxi University'

from sklearn.tree import DecisionTreeRegressor
from sklearn import preprocessing
import numpy as np
import matplotlib.pyplot as plt
import scipy.io as sio
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error
from sklearn.metrics import r2_score

# 导入数据
file = 'spectra_data.mat'
data = sio.loadmat(file)
X = data['NIR']
Y = data['octane']
print(X.shape)
print(Y.shape)

# 划分训练集/测试集
X_train, X_test, Y_train, Y_test = train_test_split(X, Y, test_size=0.2)
print(X_train.shape)
print(X_test.shape)
print(Y_train.shape)
print(Y_test.shape)

# 数据归一化
mms = preprocessing.MinMaxScaler()
X_train_mms = mms.fit_transform(X_train)
X_test_mms = mms.transform(X_test)

Y_train_mms = mms.fit_transform(Y_train)

# 建立决策树模型
tree_1 = DecisionTreeRegressor(max_depth=2)
tree_2 = DecisionTreeRegressor(max_depth=5)

# 训练决策树模型
tree_1.fit(X_train_mms, Y_train_mms)
tree_2.fit(X_train_mms, Y_train_mms)

# 决策树模型预测
Y_sim_1_mms = tree_1.predict(X_test_mms)
Y_sim_2_mms = tree_2.predict(X_test_mms)

# 反归一化
Y_sim_1 = mms.inverse_transform(Y_sim_1_mms.reshape(Y_test.shape[0], -1))
Y_sim_2 = mms.inverse_transform(Y_sim_2_mms.reshape(Y_test.shape[0], -1))

# 结果分析
MSE_1 = mean_squared_error(Y_test, Y_sim_1)
MSE_2 = mean_squared_error(Y_test, Y_sim_2)
print('[Max depth = 2] MSE = ', MSE_1)
print('[Max depth = 5] MSE = ', MSE_2)

R2_1 = r2_score(Y_test, Y_sim_1)
R2_2 = r2_score(Y_test, Y_sim_2)
print('[Max depth = 2] R2 = ', R2_1)
print('[Max depth = 5] R2 = ', R2_2)

# 绘图
plt.figure()
plt.rcParams['font.sans-serif']=['SimHei']
plt.rcParams['axes.unicode_minus'] = False

plt.scatter(np.arange(1,Y_test.shape[0]+1), Y_test, s=20, edgecolor="black",
            c="darkorange", label="True")
plt.plot(np.arange(1,Y_test.shape[0]+1), Y_sim_1, color="cornflowerblue",
         label="max_depth=2", linewidth=2)
plt.plot(np.arange(1,Y_test.shape[0]+1), Y_sim_2, color="yellowgreen",
         label="max_depth=5", linewidth=2)
plt.xticks(np.arange(0, Y_test.shape[0]+1, 3))
plt.xlabel("样本编号")
plt.ylabel("预测值")
plt.title("决策树回归模型")
plt.legend()
plt.show()

plt.figure()
min_1 = min(Y_sim_1-Y_test)
min_2 = min(Y_sim_2-Y_test)
min_y = np.floor(min(min_1, min_2))

max_1 = max(Y_sim_1-Y_test)
max_2 = max(Y_sim_2-Y_test)
max_y = np.ceil(max(max_1, max_2))

plt.subplot(2, 1, 1)
plt.bar(np.arange(Y_test.shape[0]), np.transpose(Y_sim_1-Y_test).flatten())
plt.title('预测误差')
plt.ylim(min_y, max_y)
plt.subplot(2, 1, 2)
plt.bar(np.arange(Y_test.shape[0]), np.transpose(Y_sim_2-Y_test).flatten())
plt.xlabel('样本编号')
plt.ylim(min_y, max_y)
plt.show()

