#!/usr/bin/env python 
# -*- coding:utf-8 -*-
__author__ = '<PERSON><PERSON> @ Shanxi University'

import matplotlib.pyplot as plt
import numpy as np
from sklearn import datasets, linear_model
from sklearn.metrics import mean_squared_error, r2_score
from sklearn import preprocessing
import scipy.io as sio

# 导入数据集
file = 'spectra_data.mat'
data = sio.loadmat(file)
X = data['NIR']
Y = data['octane']
print(X.shape)
print(Y.shape)

# 绘制原始光谱图
plt.figure()
plt.plot(np.arange(900, 1702, 2), np.transpose(X))
plt.xlabel('Wavelength(nm)')
plt.ylabel('Absorption')
plt.title('NIR spectrum of 60 gasoline samples')
plt.show()

# 划分训练集、验证集与测试集
k = np.random.permutation(X.shape[0])
print(k)
# 训练集
X_train = X[k[:50], :]
Y_train = Y[k[:50], :]
# 测试集
X_test = X[k[50:], :]
Y_test = Y[k[50:], :]

# 归一化
mms = preprocessing.MinMaxScaler()
X_train = mms.fit_transform(X_train) # [50, 100] ---> [0, 1]

# mms = preprocessing.MinMaxScaler()
# X_test = mms.fit_transform(X_test)  # [20, 120]  ---> [0, 1]

X_test = mms.transform(X_test)

Y_train = mms.fit_transform(Y_train)

print(X_train.shape)
print(Y_train.shape)
print(X_test.shape)
print(Y_test.shape)


# Create linear regression object
regr = linear_model.LinearRegression()
# regr = linear_model.LinearRegression(positive=True)

# Train the model using the training sets
regr.fit(X_train, Y_train)

# Make predictions using the testing set
y_pred = regr.predict(X_test)

Y_pred = mms.inverse_transform(y_pred.reshape(10, 1))

# The coefficients
print("Coefficients: \n", regr.coef_)
# The mean squared error
print("Mean squared error: %.2f" % mean_squared_error(Y_test, Y_pred))
# The coefficient of determination: 1 is perfect prediction
print("Coefficient of determination: %.2f" % r2_score(Y_test, Y_pred))

# Plot outputs
plt.scatter(Y_test, Y_pred, color="black")
plt.plot(Y_test, Y_test, color="blue", linewidth=3)

plt.xticks(())
plt.yticks(())

plt.show()