# !/usr/bin/env python
# -*- coding:utf-8 -*-
__author__ = '<PERSON><PERSON> @ Shanxi University'

import numpy as np
import scipy.io as sio
from sklearn import preprocessing
from sklearn.linear_model import RidgeCV
from sklearn.feature_selection import SelectFromModel
from sklearn.feature_selection import SequentialFeatureSelector
from time import time
import matplotlib.pyplot as plt

# 导入数据集
file = 'spectra_data.mat'
data = sio.loadmat(file)
X = data['NIR']
Y = data['octane']
print(X.shape)
print(Y.shape)

# 划分训练集、验证集与测试集
k = np.random.permutation(X.shape[0])
print(k)
# 训练集
X_train = X[k[:50], :]
Y_train = Y[k[:50], :]
# 测试集
X_test = X[k[50:], :]
Y_test = Y[k[50:], :]

# 归一化
mms = preprocessing.MinMaxScaler()
X_train = mms.fit_transform(X_train) # [50, 100] ---> [0, 1]

# mms = preprocessing.MinMaxScaler()
# X_test = mms.fit_transform(X_test)  # [20, 120]  ---> [0, 1]

X_test = mms.transform(X_test)

Y_train = mms.fit_transform(Y_train)

print(X_train.shape)
print(Y_train.shape)
print(X_test.shape)
print(Y_test.shape)

ridge = RidgeCV(alphas=np.logspace(-6, 6, num=5)).fit(X_train, Y_train)

# Selecting features with Sequential Feature Selection
tic_fwd = time()
sfs_forward = SequentialFeatureSelector(
    ridge, n_features_to_select=2, direction="forward"
).fit(X_train[:,:50], Y_train)
toc_fwd = time()

tic_bwd = time()
sfs_backward = SequentialFeatureSelector(
    ridge, n_features_to_select=2, direction="backward"
).fit(X_train[:,:50], Y_train)
toc_bwd = time()

print("Features selected by forward sequential selection: ")
print(f"Done in {toc_fwd - tic_fwd:.3f}s")
print("Features selected by backward sequential selection: ")
print(f"Done in {toc_bwd - tic_bwd:.3f}s")