#!/usr/bin/env python 
# -*- coding:utf-8 -*-
__author__ = '<PERSON><PERSON> @ Shanxi University'

import math

# region 第一个Python程序
print('Hello, World!')
print('100 + 200 + 300 = ', 100+200+300)
# endregion

# region 变量命名规则
# 区分大小写
A = 2
a = 3

# 变量名必须是字母、数字、或者下划线（_），而不能使用空格、连字符、标点符号、引号或其他字符
# 变量名的第一个字符不能是数字，而必须是字母或下划线
# b 1 = 3
# 4b = 3


# endregion

# region 数据类型
# 整型
print("整型数据：")
A = 3
print(A)

# 浮点数
print("浮点型数据：")

B = 3.1415926
print(B)

# 字符串
print("字符串型数据：")
print('I\'m \"OK\"!')
print('I\'m learning \nPython.')
print('\\\n\\''')
print(r'\\\t\\')

# 布尔型
print("布尔型数据：")
print(True)
print(False)
print(3 > 2)
print(3 >= 5)

# 逻辑与运算
print("逻辑与运算：")
print("True & True is: ", True and True)
print("True & False is: ", True and False)
print("False & False is: ", False and False)
print("5 > 3 & 3 > 1 is: ", 5 > 3 and 3 > 1)

# 逻辑或运算
print("逻辑或运算：")
print("True | True is: ", True or True)
print("True | False is: ", True or False)
print("False | False is: ", False or False)
print("5 > 3 | 3 > 1 is: ", 5 > 3 or 3 > 1)

# 逻辑非运算
print("逻辑非运算：")
print("Not True is: ", not True)
print("Not False is: ", not False)
print("not 1 > 2 is: ", not 1 > 2)
# endregion

# region 格式化输出
print("格式化输出：")
print('Hello, %s' % 'world')
print('Hello, %s' % 'Nanjing')

print("Hi, %s, you have $%d." % ("Michael", 1000))

# 常见的占位符：
#   %d    整数
#   %f    浮点数
#   %s    字符串
#   %x    十六进制整数

print("%2d-%02d" % (3, 1))  # 整数补零

PI = 3.1415926
print("%.2f" % PI)          # 小数点后保留位数

print("Growth rate: %d %%" % 7)     # 转义字符

str = "Hello, {0}, 成绩提升了 {1:.2f}%".format("小明", 18.348)
print(str)
# endregion

# region 列表(List)
print("列表(List)：")
# 定义列表
classmates = ['Michael', 'Bob', 'Tracy']
print(classmates)

# 获取列表中元素个数
print(len(classmates))

# 用索引访问列表中元素，索引从0开始
print(classmates[0])
print(classmates[1])
print(classmates[2])
# print(classmates[3])    # list index out of range

print(classmates[-1])   # 获取最后一个元素
print(classmates[-2])
print(classmates[-3])

# 添加/替换/删除元素
classmates.append('Adam')   # 追加元素到末尾
print(classmates)

classmates.insert(1, 'Jack') # 插入元素到指定位置
print(classmates)

classmates.pop()    # 删除末尾的元素
print(classmates)

classmates.pop(1)   # 删除指定位置的元素
print(classmates)

classmates[1] = 'Sarah'     # 将某个元素换成别的元素
print(classmates)

# list里的元素数据类型可以不同
L = ['Apple', 123, True]
print(L)

# list元素也可以是另一个list
s = ['Python', 'Java', ['asp', 'php'], 'scheme']
print(len(s))
print(s)
print(s[2])
print(s[2][1])
# endregion

# region 元组(Tuple)
print("元组：")
# tuple与list类似，但tuple一旦初始化就不能修改
# 因此，在定义tuple时，tuple的元素就必须被确定下来
classmates = ('Michael', 'Bob', 'Tracy')
# classmates.append('Jack')
# classmates[1] = 'Jack'
print(classmates)

# 定义只有一个元素的tuple
t = (20, )
print(t)

# "可变的"tuple
t = ('a', 'b', ['A', 'B'])
print(t)
t[2][0] = 'X'
t[2][1] = 'Y'
print(t)
# endregion

# region 字典(dict)和set
print("字典(dict)：")
names = ['Michael', 'Bob', 'Tracy']
scores = [95, 75, 85]

d = {'Michael': 95, 'Bob': 75, 'Tracy': 85}
print(d)
print(d['Michael']) # dict末尾添加新的键值对
d['Adam'] = 67
print(d)

print('Thomas' in d)    # 判断key是否存在
print(d.get('Thomas', -1))

d.pop('Bob')    # 删除指定的key
print(d)

print("set:")
s = set([1, 2, 3])
print(s)

s = set([1, 1, 2, 2, 3, 3]) # 自动过滤重复的元素
print(s)

s.add(4)    # 添加元素到set
print(s)

s.remove(4) # 删除元素
print(s)

s1 = set([1, 2, 3])     # 两个集合的交集与并集
s2 = set([2, 3, 4])
print(s1 & s2)
print(s1 | s2)
# endregion

# region 切片
L = ['Michael', 'Sarah', 'Tracy', 'Bob', 'Jack']
# 取L的前3个元素
print([L[0], L[1], L[2]])
print(L[0:3])   # 从索引0开始，直到索引为3，但不包括3
print(L[:3])    # 若第一个索引是0，可以省略
# 取L的倒数若干个元素
print(L[-2:-1])
print(L[-2:])

L = list(range(100))
print(L)
print(L[:10])       # 前10个数
print(L[-10:])      # 后10个数
print(L[10:20])     # 前11-20个数
print(L[:10:2])     # 前10个数，每两个取一个
print(L[::5])       # 所有数，每5个取一个
# endregion