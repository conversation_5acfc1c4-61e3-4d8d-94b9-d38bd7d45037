from sklearn.neural_network import MLPClassifier
from sklearn.metrics import accuracy_score, confusion_matrix

class MLPModel:
    def __init__(self, hidden_layer_sizes=(100,)):
        self.model = MLPClassifier(hidden_layer_sizes=hidden_layer_sizes, max_iter=1000)

    def fit(self, X, y):
        self.model.fit(X, y)

    def predict(self, X):
        return self.model.predict(X)

    def evaluate(self, X, y):
        y_pred = self.model.predict(X)
        return {
            "Accuracy": accuracy_score(y, y_pred),
            "ConfusionMatrix": confusion_matrix(y, y_pred).tolist()
        }
