#!/usr/bin/env python 
# -*- coding:utf-8 -*-
__author__ = '<PERSON><PERSON> @ Shanxi University'

import numpy as np
print(np.__version__)

# region 从Python列表创建数组
A = np.array([1, 4, 2, 5, 3])
print(A)
print(type(A))

# Numpy要求数组必须包含同一类型的数据
# 如果类型不匹配，Numpy将会向上转换（如果可行）
B = np.array([3.14, 2, 4, 3])
print(B)

# 明确设置数据类型
C = np.array([1, 2, 3, 4], dtype='int')
print(C)
D = np.array([1, 2, 3, 4], dtype='float32')
print(D)

# 初始化多维数组
E = np.array([range(i, i+3) for i in [2, 4, 6]])
print(E)

E = np.zeros((3,3), dtype=int)
k = 0
for i in [2, 4, 6]:
    A = range(i, i+3)
    E[k, :] = np.array(A)
    k = k + 1
print(E)

# endregion

# region 从头创建数组
# 全零数组
A = np.zeros(10, dtype=int)
print(A)

# 全1数组
B = np.ones((3, 5), dtype=float)
print(B)
C = np.full((3, 5), 3.14)
print(C)

# 线性序列数组
D = np.arange(0, 20, 2)
print(D)

# 随机数组
D = np.random.choice(10, 10, replace=False)
print(D)

# 0～1区间的均匀数组
E = np.linspace(0, 1, 5)
print(E)

# 3*3的、在0～1均匀分布的随机数组
F = np.random.random((3, 3))
print(F)

# 3*3的、均值为0、标准差为1的正态分布随机数组
G = np.random.normal(0, 1, (3, 3))
print(G)

# 3*3的，[0, 10)区间的随机整型数组
H = np.random.randint(0, 2, (3, 3))
print(H)

# 3*3的单位矩阵
I = np.eye(3)
print(I)

# endregion

# region Numpy数组的属性
np.random.seed(0)
x1 = np.random.randint(10, size=6)
x2 = np.random.randint(10, size=(3, 4))
x3 = np.random.randint(10, size=(3, 4, 5))
print("x1 = ", x1)
print("x2 = ", x2)
print("x3 = ", x3)
print("x3 ndim: ", x3.ndim)
print("x3 shape: ", x3.shape)
print("x3 size: ", x3.size)
print("x3 dtype: ", x3.dtype)
print("x3 itemsize: ", x3.itemsize, "bytes")
print("nbytes: ", x3.nbytes, "bytes")

# endregion

# region 数组索引：获取单个元素
# 方括号获取指定索引的值（下标从0开始）
x = np.array([5, 0, 3, 7, 9])
print(x[0])
print(x[4])

# 获取数组的末尾元素
print(x[-1])
print(x[-2])

# 多维数组中，用逗号分隔的索引元组获取元素
y = np.array([[3, 5, 2, 4],
              [7, 6, 8, 1],
              [0, 9, 3, 2]])
print(y[0, 0])
print(y[1, 2])
print(y[2, -1])

# 用上述索引方式修改元素值
y[0, 0] = 5
print(y)

y[0, 0] = 5.4
print(y)

# endregion

# region 数组切片：获取子数组
# 一维子数组
x = np.arange(10)
print(x)
print(x[:5])    # 前5个元素
print(x[5:])    # 索引5之后的元素
print(x[4:7])   # 索引4～7之间的子数组
print(x[::2])   # 每隔一个元素
print(x[1::2])  # 每隔一个元素，从索引1开始
print(x[::-1])  # 所有元素，逆序
print(x[5::-2]) # 从索引5开始，每隔一个元素逆序

# 多维子数组
y = np.random.randint(0, 10, (3, 4))
print(y)
print(y[:2, :3])        # 前两行，前三列
print(y[:3, ::2])       # 所有行，每隔一列
print(y[::-1, ::-1])    # 行列同时逆序
print(y[0, :])           # 第一行
print(y[0])
print(y[:, 0])           # 第一列

# 副本与视图
y = np.random.randint(0, 10, (3, 4))
print(y)
y_sub = y[:2, :2]       # y_sub是y的视图
y_sub[0, 0] = 99        # 修改y_sub中元素的值后，y中的值也被改变
print(y)

y = np.random.randint(0, 10, (3, 4))
print(y)
y_sub_copy = y[:2, :2].copy()   # y_sub_copy是y的副本
y_sub_copy[0, 0] = 99           # 修改y_sub_copy中元素的值后，y中的值不受影响
print(y_sub_copy)
print(y)

# endregion

# region 数组的变形
A = np.arange(1, 10)
B = A.reshape((3, 3))
print(B)

# 一维数组转换成二维的行或列矩阵
x = np.array([1, 2, 3])
print(x)
print(x.shape)
y = x.reshape((1, 3))
print(y)
print(y.shape)
z = x[np.newaxis, :]
print(z)
print(z.shape)

a = x.reshape((3, 1))
b = x[:, np.newaxis]
print(a)
print(a.shape)
print(b)
print(b.shape)
# endregion

# region 数组拼接和分裂
# 一维数组的拼接
x = np.array([1, 2, 3])
y = np.array([3, 2, 1])
z = np.concatenate([x, y])
print(z)
print(np.concatenate([x, y, z]))

# 二维数组的拼接
A = np.random.randint(0, 10, (2, 4))
print("A = \n", A)
B = np.random.randint(0, 10, (2, 4))
print("B = \n", B)

C = np.concatenate([A, B])          # 按行的方向拼接
C = np.concatenate([A, B], axis=0)
C = np.vstack([A, B])
print("C = \n", C)

D = np.concatenate([A, B], axis=1)  # 按列的方向拼接
D = np.hstack([A, B])
print("D = \n", D)

# 数组的分裂
# 一维数组的分裂
x = [1, 2, 3, 7, 4, 6, 9, 5, 0]
print(x)
x1, x2, x3 = np.split(x, [3, 5])
print(x1)
print(x2)
print(x3)

# 二维数组的分裂
A = np.random.randint(0, 10, [3, 5])
print(A)
B, C = np.vsplit(A, [1])
print(B)
print(C)

D, E = np.hsplit(A, [3])
print(D)
print(E)

# endregion

# region 数组的运算
x = np.arange(4)
print("x = ", x)
print("x + 5 = ", x + 5)                # np.add(x, 5)
print("x - 5 = ", x - 5)                # np.subtract(x, 5)
print("x * 2 = ", x * 2)                # np.multiply(x, 2)
print("x / 2 = ", x / 2)                # np.divide(x, 2)
print("x // 2 = ", x // 2)  # 向下整除  # np.floor_divide(x, 2)
print("-x = ", -x)                      # np.negative(x)
print("x ** 2 = ", x ** 2)  # 指数      # np.power(x, 2)
print("x % 2 = ", x % 2)    # 模         # np.mod(x, 2)

print(abs(x))       # 绝对值
print(np.abs(x))
print(np.absolute(x))

theta = np.linspace(0, np.pi, 3)    # 三角函数
print("theta = ", theta)
print("sin(theta) = ", np.sin(theta))       # np.arcsin(x)
print("cos(theta) = ", np.cos(theta))       # np.arccos(x)
print("tan(theta) = ", np.tan(theta))       # np.arctan(x)

x = [1, 2, 3]       # 指数和对数
print("e^x = ", np.exp(x))          # np.log(x)
print("2^x = ", np.exp2(x))         # np.log2(x)
print("3^x = ", np.power(3, x))     # np.log10(x)

L = np.random.randint(0, 10, (3, 5))
print("L = \n", L)
print("sum(L) = ", np.sum(L))       # 求和
print(np.sum(L, axis=0))
print(np.sum(L, axis=1))

print("min(L) = ", np.min(L))       # 最小值
print(np.min(L, axis=0))
print(np.min(L, axis=1))

print("max(L) = ", np.max(L))       # 最大值
print(np.max(L, axis=0))
print(np.max(L, axis=1))

# endregion

# region 比较操作
x = np.array([1, 4, 5, 3, 6, 2])

print(x > 3)                    # 大于
print("x > 3: ", x[x > 3])

print(x >= 3)                    # 大于等于
print("x >= 3: ", x[x >= 3])

print(x < 3)                    # 小于
print("x < 3: ", x[x < 3])

print(x <= 3)                    # 小于等于
print("x <= 3: ", x[x <= 3])

print(x == 3)                   # 等于
print("x == 3: ", x[x == 3])

print(x != 3)                   # 不等于
print("x != 3: ", x[x != 3])

# 统计记录的个数
print(np.count_nonzero(x > 3))
print(np.sum(x > 3))

# 检查是否满足特定条件
print(np.any(x > 4))
print(np.all(x > 4))

# 布尔运算符
x = np.array([1, 4, 5, 3, 6, 2])
print(np.sum((x > 1) & (x < 5)))
print(np.sum((x < 1) | (x > 5)))
print(np.sum(~((x <= 1) | (x >= 5))))

# endregion

# region 数组排序
x = np.array([2, 1, 4, 3, 5])
# print(np.sort(x))
x.sort()
print(x)

x = np.array([2, 1, 4, 3, 4, 5])
i = np.argsort(x)               # 排序索引值
print(i)
# endregion




