from PyQt5.QtWidgets import QMessageBox, QWidget, QVBoxLayout, QPushButton, QFileDialog, QLabel
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from sklearn.metrics import mean_squared_error, r2_score, confusion_matrix, ConfusionMatrixDisplay
from utils.plot_style import apply_plot_style

class EvaluationTab(QWidget):
    def __init__(self):
        super().__init__()
        self.y_true = None
        self.y_pred = None

        layout = QVBoxLayout()

        load_btn = QPushButton("导入真实值与预测值")
        load_btn.clicked.connect(self.load_data)

        self.canvas = FigureCanvas(plt.Figure(figsize=(7, 4)))
        layout.addWidget(load_btn)
        layout.addWidget(self.canvas)
        self.ax = self.canvas.figure.subplots()

        self.setLayout(layout)

    def load_data(self):
        ytrue_path, _ = QFileDialog.getOpenFileName(self, "选择真实值 CSV", "", "CSV Files (*.csv)")
        ypred_path, _ = QFileDialog.getOpenFileName(self, "选择预测值 CSV", "", "CSV Files (*.csv)")
        if ytrue_path and ypred_path:
            y_true_df = pd.read_csv(ytrue_path)
            y_pred_df = pd.read_csv(ypred_path)
            self.y_true = y_true_df.to_numpy().ravel()
            self.y_pred = y_pred_df.to_numpy().ravel()
            self.plot_evaluation()

    def plot_evaluation(self):
        if self.model is None:
            QMessageBox.warning(self, '错误', '请先加载模型')
            return
        if self.X is None or self.y is None:
            QMessageBox.warning(self, '错误', '请先加载数据')
            return
        if self.X.shape[0] != self.y.shape[0]:
            QMessageBox.critical(self, '错误', 'X 和 y 的样本数不一致')
            return
        self.ax.clear()
        if len(np.unique(self.y_true)) <= 10 and set(np.unique(self.y_true)).issubset({0,1}):
            # 分类：混淆矩阵
            cm = confusion_matrix(self.y_true, self.y_pred)
            disp = ConfusionMatrixDisplay(confusion_matrix=cm)
            disp.plot(ax=self.ax)
            self.ax.set_title("分类评估：混淆矩阵")
        else:
            # 回归：真实 vs 预测
            rmse = mean_squared_error(self.y_true, self.y_pred, squared=False)
            r2 = r2_score(self.y_true, self.y_pred)
            self.ax.scatter(self.y_true, self.y_pred, alpha=0.6)
            self.ax.plot([self.y_true.min(), self.y_true.max()],
                         [self.y_true.min(), self.y_true.max()], 'r--')
            self.ax.set_xlabel("真实值")
            self.ax.set_ylabel("预测值")
            self.ax.set_title(f"回归评估 RMSE={rmse:.3f}, R²={r2:.3f}")

        apply_plot_style(self.ax, theme="light")
        self.canvas.draw()
        QMessageBox.information(self, '提示', '评估完成')


    def export_figure(self):
        if self.canvas:
            path, _ = QFileDialog.getSaveFileName(self, "保存图像", "evaluation_plot", "图像文件 (*.png *.pdf *.svg)")
            if path:
                self.canvas.figure.savefig(path, bbox_inches='tight', dpi=300)
