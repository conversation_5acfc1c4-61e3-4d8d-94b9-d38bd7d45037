import torch
import torch.nn.functional as F

import numpy as np
import scipy.io as sio
import matplotlib.pyplot as plt
from sklearn import preprocessing

# 导入数据
file = 'spectra_data.mat'
data = sio.loadmat(file)
X = data['NIR']
Y = data['octane']
print(X.shape)
print(Y.shape)

# 绘制原始光谱图
plt.plot(np.arange(900, 1702, 2), np.transpose(X))
plt.xlabel('Wavelength(nm)')
plt.ylabel('Absorption')
plt.title('NIR spectrum of 60 gasoline samples')
plt.show()

# 拆分训练集、测试集
k = np.random.permutation(X.shape[0])
print(k)
X_train = X[k[:50], :]
Y_train = Y[k[:50], :]

X_test = X[k[50:], :]
Y_test = Y[k[50:], :]

# 归一化预处理
mms = preprocessing.MinMaxScaler()
X_train = mms.fit_transform(X_train)
X_test = mms.transform(X_test)

Y_train = mms.fit_transform(Y_train)

print(X_train.shape)
print(Y_train.shape)
print(X_test.shape)
print(Y_test.shape)

# 转换成tensor格式
xtrain = torch.unsqueeze(torch.tensor(X_train).float(), dim=1)   #升维
xtest = torch.unsqueeze(torch.tensor(X_test).float(), dim=1)
print(xtrain.shape)
ytrain = torch.tensor(Y_train).float()
ytest = torch.tensor(Y_test).float()


# 通过定义一个Net类来建立神经网络
class Net(torch.nn.Module):
    def __init__(self, n_feature, n_hidden, n_output):
        super(Net, self).__init__()
        self.hidden = torch.nn.Linear(n_feature, n_hidden)
        self.predict = torch.nn.Linear(n_hidden, n_output)

    def forward(self, x):
        print(x.shape)
        x = F.relu(self.hidden(x))
        x = self.predict(x)
        return x


net = Net(401, 12, 1)

# 设置优化器和损失函数
optimizer = torch.optim.SGD(net.parameters(), lr=0.01)  # 梯度下降方法
loss_function = torch.nn.MSELoss()  # 计算误差方法


# 训练网络
for i in range(500):
    prediction = net(xtrain)
    prediction = prediction.squeeze(-1)
    loss = loss_function(prediction, ytrain)
    if loss.data <= 1e-5:
        break
    optimizer.zero_grad()  # 消除梯度
    loss.backward()  # 反向传播
    optimizer.step()  # 执行

# 测试集预测
y_sim = net(xtest)
print(y_sim.shape)
y_sim = y_sim.squeeze(-1)
print(y_sim.shape)
y_sim = mms.inverse_transform(y_sim.data.numpy().reshape(10, -1))

# 误差分析
error = abs(ytest.data.numpy()-y_sim) / ytest.data.numpy()

results = np.hstack((ytest.data.numpy(), y_sim, error))
print(results)


# 计算决定系数R2
def compute_correlation(x,y):
    xbar = np.mean(x)
    ybar = np.mean(y)
    ssr = 0.0
    var_x = 0.0
    var_y = 0.0
    for i in range(0,len(x)):
        diff_xbar = x[i] - xbar
        dif_ybar = y[i] - ybar
        ssr += (diff_xbar * dif_ybar)
        var_x += diff_xbar**2
        var_y += dif_ybar**2
    sst = np.sqrt(var_x * var_y)
    return ssr/sst


R = compute_correlation(y_sim, ytest.data.numpy())
print("R2 = ", R**2)

# 绘图
plt.title('Prediction Results')
plt.scatter(ytest.data.numpy(), y_sim)
plt.plot(ytest.data.numpy(), ytest.data.numpy(),'r--')
plt.show()

# 查看模型参数
print(net.parameters())
paras = list(net.parameters())
print(paras)
for num, para in enumerate(paras):
    print('number:', num)
    print(para.size())
    print(para)
    print('_____________________________')