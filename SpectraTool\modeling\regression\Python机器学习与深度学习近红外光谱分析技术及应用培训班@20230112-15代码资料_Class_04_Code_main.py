from sklearn.neural_network import MLPRegressor
from sklearn import preprocessing
import numpy as np
import matplotlib.pyplot as plt
import scipy.io as sio

from sklearn.model_selection import cross_val_score

# 导入数据集
file = 'spectra_data.mat'
data = sio.loadmat(file)
X = data['NIR']
Y = data['octane']
print(X.shape)
print(Y.shape)

# 绘制原始光谱图
plt.figure()
plt.plot(np.arange(900, 1702, 2), np.transpose(X))
plt.xlabel('Wavelength(nm)')
plt.ylabel('Absorption')
plt.title('NIR spectrum of 60 gasoline samples')
plt.show()

# 划分训练集、验证集与测试集
k = np.random.permutation(X.shape[0])
print(k)
X_train = X[k[:50], :]
Y_train = Y[k[:50], :]
   # 训练集

X_test = X[k[50:], :]       # 测试集
Y_test = Y[k[50:], :]

# 归一化
mms = preprocessing.MinMaxScaler()
X_train = mms.fit_transform(X_train) # [50, 100] ---> [0, 1]

# mms = preprocessing.MinMaxScaler()
# X_test = mms.fit_transform(X_test)  # [20, 120]  ---> [0, 1]

X_test = mms.transform(X_test)

Y_train = mms.fit_transform(Y_train)

print(X_train.shape)
print(Y_train.shape)
print(X_test.shape)
print(Y_test.shape)

# 建立MLP模型
nn = MLPRegressor(hidden_layer_sizes=(200, ),
                  activation='tanh',
                  solver='adam',
                  learning_rate='constant',
                  learning_rate_init=0.001,
                  max_iter=1000,
                  tol=0.00001)
# 训练MLP模型
nn.fit(X_train, Y_train.ravel())

# MLP模型预测
y_sim = nn.predict(X_test)
Y_sim = mms.inverse_transform(y_sim.reshape(10, 1))

Error = np.abs(Y_sim-Y_test) / Y_test
Result = np.hstack((Y_sim, Y_test, Error))
print(Result)

# region 查看模型参数
print(nn.coefs_[0].shape)   # 输入层与隐含层之间的连接权值
print(nn.coefs_[1].shape)   # 隐含层与输出层之间的连接权值
print(nn.intercepts_[0].shape)  # 隐含层神经元的阈值
print(nn.intercepts_[1].shape)  # 输出层神经元的阈值

print(nn.loss_)
print(nn.n_iter_)
print(nn.n_layers_)
# endregion

def compute_correlation(x,y):
    xbar = np.mean(x)
    ybar = np.mean(y)
    ssr = 0.0
    var_x = 0.0
    var_y = 0.0
    for i in range(0,len(x)):
        diff_xbar = x[i] - xbar
        dif_ybar = y[i] - ybar
        ssr += (diff_xbar * dif_ybar)
        var_x += diff_xbar**2
        var_y += dif_ybar**2
    sst = np.sqrt(var_x * var_y)
    return ssr/sst


R = compute_correlation(Y_sim, Y_test)
print(R**2)

# region 交叉验证(Cross Validation, CV)
scores = cross_val_score(nn, X_train, Y_train.ravel(), cv=5)
print(scores)
print("Mean Score:", np.mean(scores), ", Max Score:", np.max(scores), ", Min Score:", np.min(scores))
# endregion

# region 绘制迭代过程曲线
plt.plot(np.arange(nn.n_iter_), nn.loss_curve_)
plt.xlim((0, nn.n_iter_))
plt.xlabel("Iterative Epochs")
plt.ylabel("Loss")
plt.title("Iterative Curve")
plt.show()
# endregion