import sys
from PyQt5.QtWidgets import (
    QFrame,
    QApplication, QMainWindow, QWidget, QVBoxLayout, QLabel, QPushButton,
    QComboBox, QTabWidget, QFileDialog, QHBoxLayout, QSplitter, QListWidget
)
from PyQt5.QtCore import Qt
from gui.preprocess_tab import PreprocessTab
from gui.wavelength_tab import WavelengthTab
from gui.modeling_tab import ModelingTab
from gui.modeling_batch_tab import BatchModelingTab
from gui.evaluation_tab import EvaluationTab
from gui.export_tab import ExportTab
from gui.oneclick_tab import OneClickModelTab
from utils.plot_style import apply_plot_style

# 多语言支持字典
lang_dict = {
    'en': {
        'title': 'SpectraTool - Spectral Analysis Platform',
        'load_data': 'Load Data',
        'preprocess': 'Preprocessing',
        'split': 'Sample Split',
        'outlier': 'Outlier Removal',
        'wavelength': 'Wavelength Selection',
        'modeling': 'Modeling',
        'visual': 'Visualization',
        'batch': 'Batch Process',
        'language': 'Language'
    },
    'zh': {
        'title': 'SpectraTool - 光谱分析平台',
        'load_data': '导入数据',
        'preprocess': '光谱预处理',
        'split': '样本划分',
        'outlier': '异常剔除',
        'wavelength': '波长选择',
        'modeling': '建模与预测',
        'visual': '结果可视化',
        'batch': '批量处理',
        'language': '语言切换'
    }
}

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.language = 'zh'
        self.theme = 'light'
        self.setWindowTitle(lang_dict[self.language]['title'])
        self.resize(1200, 800)

        # 主界面结构
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        main_layout = QHBoxLayout()
        self.central_widget.setLayout(main_layout)

        # 左侧导航栏
        self.nav_list = QListWidget()
        self.nav_items = ['load_data', 'preprocess', 'split', 'outlier',
                          'wavelength', 'modeling', 'visual', 'batch']
                                                                for key in self.nav_items:
            if key == 'preprocess':
                tab = PreprocessTab()
            elif key == 'wavelength':
                tab = WavelengthTab()
            elif key == 'modeling':
                tab = ModelingTab()
            elif key == 'batch':
                tab = BatchModelingTab()
            elif key == 'visual':
                tab = EvaluationTab()
            elif key == 'export':
                tab = ExportTab()
            elif key == 'oneclick':
                tab = OneClickModelTab()
            else:
                tab = QWidget()
                layout = QVBoxLayout()
                label = QLabel(f"{lang_dict[self.language][key]} 模块区域")
                layout.addWidget(label)
                tab.setLayout(layout)
            self.tabs.addTab(tab, lang_dict[self.language][key])








        # 多语言切换下拉框
        self.lang_combo = QComboBox()
        self.lang_combo.addItems(['中文', 'English'])
        self.lang_combo.currentIndexChanged.connect(self.switch_language)

        top_bar = QHBoxLayout()
        top_bar.addWidget(QLabel(lang_dict[self.language]['language']))
        top_bar.addWidget(self.lang_combo)
        top_bar.addStretch()

        top_wrapper = QVBoxLayout()
        top_wrapper.addLayout(top_bar)
        top_wrapper.addWidget(self.tabs)

        splitter = QSplitter(Qt.Horizontal)
        splitter.addWidget(self.nav_list)
        content_widget = QWidget()
        content_widget.setLayout(top_wrapper)
        splitter.addWidget(content_widget)
        splitter.setSizes([200, 1000])

        main_layout.addWidget(splitter)

    def change_tab(self, index):
        self.tabs.setCurrentIndex(index)

    def switch_language(self, index):
        self.language = 'zh'
        self.theme = 'light' if index == 0 else 'en'
        self.setWindowTitle(lang_dict[self.language]['title'])
        for i, key in enumerate(self.nav_items):
            self.nav_list.item(i).setText(lang_dict[self.language][key])
            self.tabs.setTabText(i, lang_dict[self.language][key])
            self.tabs.widget(i).layout().itemAt(0).widget().setText(f"{lang_dict[self.language][key]} 模块区域")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())


    def toggle_theme(self):
        self.theme = 'dark' if self.theme == 'light' else 'light'
        self.setStyleSheet("background-color: #2b2b2b;" if self.theme == 'dark' else "")
        for i in range(self.tabs.count()):
            widget = self.tabs.widget(i)
            if hasattr(widget, "canvas"):
                apply_plot_style(widget.ax, theme=self.theme)
                widget.canvas.draw()
    