from modeling import MODEL_REGISTRY
from PyQt5.QtWidgets import Q<PERSON>essageBox, (
    QWidget, QVBoxLayout, QPushButton, QFileDialog, QLabel, QHBoxLayout, QComboBox
)
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
import matplotlib.pyplot as plt
from utils.plot_style import apply_plot_style
import numpy as np
import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.metrics import r2_score, mean_squared_error, accuracy_score
from sklearn.cross_decomposition import PLSRegression
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.svm import SVR, SVC
from xgboost import XGBRegressor, XGBClassifier

class ModelingTab(QWidget):
    def __init__(self):
        super().__init__()
        self.X = None
        self.y = None
        self.model = None
        self.y_pred = None
        self.mode = "Regression"

        layout = QVBoxLayout()

        load_btn = QPushButton("导入 X / y 数据")
        load_btn.clicked.connect(self.load_data)

        self.model_select = QComboBox()
        self.model_select.addItems(["PLSR", "RF", "SVM", "XGB"])
        self.task_select = QComboBox()
        self.task_select.addItems(["Regression", "Classification"])
        run_btn = QPushButton("开始建模")
        run_btn.clicked.connect(self.run_modeling)

        select_layout = QHBoxLayout()
        select_layout.addWidget(QLabel("建模类型:"))
        select_layout.addWidget(self.task_select)
        select_layout.addWidget(QLabel("模型选择:"))
        select_layout.addWidget(self.model_select)
        select_layout.addStretch()

        layout.addWidget(load_btn)
        layout.addLayout(select_layout)
        layout.addWidget(run_btn)

        # 图像显示
        self.canvas = FigureCanvas(plt.Figure(figsize=(6, 4)))
        layout.addWidget(export_btn)
        layout.addWidget(self.canvas)
        self.ax = self.canvas.figure.subplots()

        self.setLayout(layout)

    def load_data(self):
        x_path, _ = QFileDialog.getOpenFileName(self, "选择 X 数据", "", "CSV Files (*.csv)")
        y_path, _ = QFileDialog.getOpenFileName(self, "选择 y 数据", "", "CSV Files (*.csv)")
        if x_path and y_path:
            x_df = pd.read_csv(x_path)
            y_df = pd.read_csv(y_path)
            self.X = x_df.to_numpy()
            self.y = y_df.to_numpy().ravel()

    def run_modeling(self):
        if self.X is None or self.y is None:
            return

        task = self.task_select.currentText()
        model_name = self.model_select.currentText()
        X_train, X_test, y_train, y_test = train_test_split(self.X, self.y, test_size=0.3, random_state=42)

        if task == "Regression":
            if model_name == "PLSR":
                self.model = PLSRegression(n_components=10)
            elif model_name == "RF":
                self.model = RandomForestRegressor(n_estimators=100)
            elif model_name == "SVM":
                self.model = SVR()
            elif model_name == "XGB":
                self.model = XGBRegressor()
        else:
            if model_name == "RF":
                self.model = RandomForestClassifier(n_estimators=100)
            elif model_name == "SVM":
                self.model = SVC()
            elif model_name == "XGB":
                self.model = XGBClassifier()
            else:
                self.ax.clear()
                self.ax.set_title("PLSR 不适用于分类任务")
                self.canvas.draw()
                return

        self.model.fit(X_train, y_train)
        self.y_pred = self.model.predict(X_test)

        self.ax.clear()
        if task == "Regression":
            rmse = mean_squared_error(y_test, self.y_pred, squared=False)
            r2 = r2_score(y_test, self.y_pred)
            self.ax.scatter(y_test, self.y_pred, alpha=0.6)
            self.ax.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--')
            self.ax.set_title(f"回归预测
RMSE={rmse:.3f}, R²={r2:.3f}")
            self.ax.set_xlabel("真实值"); self.ax.set_ylabel("预测值")
        apply_plot_style(self.ax, theme='light')
        else:
            acc = accuracy_score(y_test, self.y_pred)
            self.ax.set_title(f"分类预测
准确率={acc:.3f}")
            self.ax.plot(self.y_pred[:30], 'ro-', label="预测")
            self.ax.plot(y_test[:30], 'bo-', label="真实")
            self.ax.legend()

        self.canvas.draw()


    def export_figure(self):
        if self.canvas:
            path, _ = QFileDialog.getSaveFileName(self, "保存图像", "model_plot", "图像文件 (*.png *.pdf *.svg)")
            if path:
                self.canvas.figure.savefig(path, bbox_inches='tight', dpi=300)
