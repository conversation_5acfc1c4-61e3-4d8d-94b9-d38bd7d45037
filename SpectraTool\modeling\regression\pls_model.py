from sklearn.cross_decomposition import PLSRegression
from sklearn.metrics import mean_squared_error, r2_score

class PLSModel:
    def __init__(self, n_components=10):
        self.model = PLSRegression(n_components=n_components)

    def fit(self, X, y):
        self.model.fit(X, y)

    def predict(self, X):
        return self.model.predict(X).ravel()

    def evaluate(self, X, y):
        y_pred = self.predict(X)
        return {
            "RMSE": mean_squared_error(y, y_pred, squared=False),
            "R2": r2_score(y, y_pred)
        }
