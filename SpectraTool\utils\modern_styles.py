#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
现代化界面样式定义
"""

def get_main_window_style():
    """获取主窗口样式"""
    return """
    QMainWindow {
        background-color: #f8f9fa;
        color: #2c3e50;
    }
    
    QWidget {
        font-family: "Microsoft YaHei", "Segoe UI", Arial, sans-serif;
        font-size: 9pt;
    }
    """

def get_navigation_style():
    """获取导航栏样式"""
    return """
    QListWidget {
        background-color: #34495e;
        color: white;
        border: none;
        outline: none;
        font-size: 11pt;
        font-weight: bold;
        padding: 5px;
    }
    
    QListWidget::item {
        padding: 12px 15px;
        border-bottom: 1px solid #2c3e50;
        margin: 2px 0px;
    }
    
    QListWidget::item:hover {
        background-color: #3498db;
        border-radius: 5px;
    }
    
    QListWidget::item:selected {
        background-color: #2980b9;
        border-radius: 5px;
        color: white;
    }
    """

def get_tab_widget_style():
    """获取标签页样式"""
    return """
    QTabWidget::pane {
        border: 1px solid #bdc3c7;
        background-color: white;
        border-radius: 8px;
        margin-top: -1px;
    }
    
    QTabWidget::tab-bar {
        alignment: left;
    }
    
    QTabBar::tab {
        background-color: #ecf0f1;
        color: #2c3e50;
        padding: 10px 20px;
        margin-right: 2px;
        border-top-left-radius: 8px;
        border-top-right-radius: 8px;
        font-weight: bold;
        min-width: 100px;
    }
    
    QTabBar::tab:hover {
        background-color: #d5dbdb;
    }
    
    QTabBar::tab:selected {
        background-color: white;
        color: #2980b9;
        border-bottom: 3px solid #3498db;
    }
    """

def get_button_style():
    """获取按钮样式"""
    return """
    QPushButton {
        background-color: #3498db;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 6px;
        font-weight: bold;
        font-size: 10pt;
        min-height: 15px;
    }
    
    QPushButton:hover {
        background-color: #2980b9;
        transform: translateY(-1px);
    }
    
    QPushButton:pressed {
        background-color: #21618c;
        transform: translateY(1px);
    }
    
    QPushButton:disabled {
        background-color: #bdc3c7;
        color: #7f8c8d;
    }
    
    /* 特殊按钮样式 */
    QPushButton[buttonType="success"] {
        background-color: #27ae60;
    }
    
    QPushButton[buttonType="success"]:hover {
        background-color: #229954;
    }
    
    QPushButton[buttonType="warning"] {
        background-color: #f39c12;
    }
    
    QPushButton[buttonType="warning"]:hover {
        background-color: #e67e22;
    }
    
    QPushButton[buttonType="danger"] {
        background-color: #e74c3c;
    }
    
    QPushButton[buttonType="danger"]:hover {
        background-color: #c0392b;
    }
    """

def get_input_style():
    """获取输入控件样式"""
    return """
    QLineEdit, QTextEdit, QPlainTextEdit {
        border: 2px solid #bdc3c7;
        border-radius: 6px;
        padding: 8px;
        background-color: white;
        font-size: 10pt;
    }
    
    QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
        border-color: #3498db;
        outline: none;
    }
    
    QComboBox {
        border: 2px solid #bdc3c7;
        border-radius: 6px;
        padding: 8px;
        background-color: white;
        font-size: 10pt;
        min-width: 100px;
    }
    
    QComboBox:focus {
        border-color: #3498db;
    }
    
    QComboBox::drop-down {
        border: none;
        width: 20px;
    }
    
    QComboBox::down-arrow {
        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iNiIgdmlld0JveD0iMCAwIDEwIDYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik01IDZMMCAwSDEwTDUgNloiIGZpbGw9IiM3Rjg4OEQiLz4KPC9zdmc+);
    }
    
    QSpinBox {
        border: 2px solid #bdc3c7;
        border-radius: 6px;
        padding: 8px;
        background-color: white;
        font-size: 10pt;
    }
    
    QSpinBox:focus {
        border-color: #3498db;
    }
    """

def get_checkbox_style():
    """获取复选框样式"""
    return """
    QCheckBox {
        font-size: 10pt;
        font-weight: bold;
        color: #2c3e50;
        spacing: 8px;
    }
    
    QCheckBox::indicator {
        width: 18px;
        height: 18px;
        border: 2px solid #bdc3c7;
        border-radius: 4px;
        background-color: white;
    }
    
    QCheckBox::indicator:hover {
        border-color: #3498db;
    }
    
    QCheckBox::indicator:checked {
        background-color: #3498db;
        border-color: #3498db;
        image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4=);
    }
    """

def get_group_box_style():
    """获取分组框样式"""
    return """
    QGroupBox {
        font-weight: bold;
        font-size: 11pt;
        color: #2c3e50;
        border: 2px solid #bdc3c7;
        border-radius: 8px;
        margin: 10px 5px;
        padding-top: 15px;
        background-color: white;
    }
    
    QGroupBox::title {
        subcontrol-origin: margin;
        left: 15px;
        padding: 0 8px 0 8px;
        color: #2980b9;
        font-weight: bold;
        background-color: white;
    }
    """

def get_table_style():
    """获取表格样式"""
    return """
    QTableWidget {
        gridline-color: #ecf0f1;
        background-color: white;
        alternate-background-color: #f8f9fa;
        border: 1px solid #bdc3c7;
        border-radius: 6px;
        font-size: 9pt;
    }
    
    QTableWidget::item {
        padding: 8px;
        border: none;
    }
    
    QTableWidget::item:selected {
        background-color: #3498db;
        color: white;
    }
    
    QHeaderView::section {
        background-color: #34495e;
        color: white;
        padding: 10px;
        border: none;
        font-weight: bold;
    }
    
    QHeaderView::section:hover {
        background-color: #2c3e50;
    }
    """

def get_progress_bar_style():
    """获取进度条样式"""
    return """
    QProgressBar {
        border: 2px solid #bdc3c7;
        border-radius: 8px;
        text-align: center;
        font-weight: bold;
        background-color: #ecf0f1;
    }
    
    QProgressBar::chunk {
        background-color: #3498db;
        border-radius: 6px;
    }
    """

def get_scroll_bar_style():
    """获取滚动条样式"""
    return """
    QScrollBar:vertical {
        background-color: #ecf0f1;
        width: 12px;
        border-radius: 6px;
    }
    
    QScrollBar::handle:vertical {
        background-color: #bdc3c7;
        border-radius: 6px;
        min-height: 20px;
    }
    
    QScrollBar::handle:vertical:hover {
        background-color: #95a5a6;
    }
    
    QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
        height: 0px;
    }
    
    QScrollBar:horizontal {
        background-color: #ecf0f1;
        height: 12px;
        border-radius: 6px;
    }
    
    QScrollBar::handle:horizontal {
        background-color: #bdc3c7;
        border-radius: 6px;
        min-width: 20px;
    }
    
    QScrollBar::handle:horizontal:hover {
        background-color: #95a5a6;
    }
    
    QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
        width: 0px;
    }
    """

def get_complete_style():
    """获取完整的应用样式"""
    return (
        get_main_window_style() +
        get_navigation_style() +
        get_tab_widget_style() +
        get_button_style() +
        get_input_style() +
        get_checkbox_style() +
        get_group_box_style() +
        get_table_style() +
        get_progress_bar_style() +
        get_scroll_bar_style()
    )
