clc
clear

%% 导入数据

Data = xlsread('小麦248.xlsx');
data = Data(2:end,:);  %光谱阵和理化值
nm = Data(1,:);   %波段
X = data(:,1:end-1);    %光谱阵
y = data(:,end);       %理化值

%% 随机划分

data = [X y];
% 生成随机排列的索引
idx = randperm(size(data, 1));

% 按照一定比例划分训练集和测试集
train_ratio = 0.75; % 训练集占总数据集的比例
num_train = round(train_ratio * size(data, 1)); % 计算训练集大小

% 获取训练集和测试集的索引
train_idx = idx(1:num_train);
test_idx = idx(num_train+1:end);

% 根据索引划分特征集（X）和标签集（Y）
Xtrain = data(train_idx, 1:end-1); % 去除最后一列作为特征
Ytrain = data(train_idx, end); % 最后一列作为标签
Xtest = data(test_idx, 1:end-1); % 去除最后一列作为特征
Ytest = data(test_idx, end); % 最后一列作为标签

% 输出训练集和测试集的大小
fprintf('训练集特征大小：%d x %d\n', size(Xtrain));
fprintf('训练集标签大小：%d x 1\n', size(Ytrain));
fprintf('测试集特征大小：%d x %d\n', size(Xtest));
fprintf('测试集标签大小：%d x 1\n', size(Ytest));

%% 划分统计结果
% 计算统计信息
train_stats = [size(Ytrain, 1), max(Ytrain), min(Ytrain), mean(Ytrain), std(Ytrain)];
test_stats = [size(Ytest, 1), max(Ytest), min(Ytest), mean(Ytest), std(Ytest)];
all_stats = [size(y, 1), max(y), min(y), mean(y), std(y)];
% 创建表格
table_data = [train_stats; test_stats; all_stats];
T = array2table(table_data, 'VariableNames', {'SampleCount', 'MaxValue', 'MinValue', 'Mean', 'StdDev'}, ...
    'RowNames', {'TrainSet', 'TestSet', 'AllSamples'});
% 显示表格
disp(T);
