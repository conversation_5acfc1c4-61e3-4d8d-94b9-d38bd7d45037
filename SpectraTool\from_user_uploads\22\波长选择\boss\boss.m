function BOSS=boss(Xcal,ycal,nLV_max,fold,method,num_bootstrap,flag,speed)

%%        Initial settings
if nargin<8; speed=0;end;
if nargin<7; flag=0;end;
if nargin<6; num_bootstrap=1000;end;
if nargin<5; method=('center');end;
if nargin<4; fold=5;end;
if nargin<3; nLV_max=10;end

if num_bootstrap>=2000 
    ratio=0.05;
else if num_bootstrap<2000 && num_bootstrap>=1000 
        ratio=0.1;
    else if num_bootstrap<1000 && num_bootstrap>=500
            ratio=0.2;
        else if num_bootstrap<500
                fprintf('The number of bootstrap sampling ought to be larger than 500')
                BBS=[];
                return
            end
        end
    end
end


[~,n]=size(Xcal);
num_retained=n;
num_best=round(num_bootstrap*ratio);
w=ones(n,1)/n;
Variable=zeros(n,100);
W=zeros(n,100);
RMSECV=zeros(1,100);
for j=1:100
variable_index=zeros(n,num_bootstrap);
for i=1:num_bootstrap
    Vsel=randsample(n,num_retained,true,w); 
    Vsel=unique(Vsel);
    variable_index(Vsel,i)=1;
end

B=zeros(n,num_bootstrap);
for i=1:num_bootstrap
    vsel=find(variable_index(:,i)==1);
    CV=plscvfold(Xcal(:,vsel),ycal,nLV_max,fold,method);
    rmsecv(i)=CV.RMSECV;
    PLS=pls(Xcal(:,vsel),ycal,CV.optPC,method);
    coef=PLS.regcoef_pretreat;
    coef=coef/norm(coef);
    b=zeros(n,1);
    b(vsel)=coef;
    B(:,i)=b;
end
[rmsecv_sort,index]=sort(rmsecv);
RMSECV(j)=rmsecv(index(1));
Variable(:,j)=variable_index(:,index(1));
%w=sum(B,2);
w=abs(sum(B(:,index(1:num_best)),2));
% w=sum(variable_index(:,index(1:num_best)),2);
w=w/norm(w);
W(:,j)=w;
    if flag==1
    figure(1)
    bar(w)
    xlabel('varaible')
    ylabel('weight')
    axis([0 n 0 max(w)*1.1])
    drawnow
    end
% figure
% plot(w)
% figure
% imagesc(B(:,index)')

% figure
% plot(sum(B(:,index(1:500))'))
if speed==0
num_retained=sum(sum(variable_index))/num_bootstrap;
elseif speed==1
     num_retained=sum(sum(variable_index))/num_bootstrap*1.1;
     elseif speed==2
            num_retained=sum(sum(variable_index))/num_bootstrap*1.2;
            elseif speed==3
                num_retained=sum(sum(variable_index))/num_bootstrap*1.3;
                elseif speed==4
                    num_retained=sum(sum(variable_index))/num_bootstrap*1.4;
elseif speed==5
     num_retained=sum(sum(variable_index))/num_bootstrap*1.5; 
end
            
% num_retained=sum(sum(variable_index))/num_bootstrap;
num_retained=floor(num_retained);
Num_retained(i)=num_retained;
if num_retained<=1
    break
end
j
end

if j<100
W(:,j+1:100)=[];
RMSECV(j+1:end)=[];
end

in=find(RMSECV==min(RMSECV));
in=in(end);

CV=plscvfold(Xcal(:,Variable(:,in)==1),ycal,nLV_max,fold,method);


BOSS.minRMSECV=CV.RMSECV;
BOSS.RMSECV_all=RMSECV;
BOSS.Q2_max=CV.Q2_max;
BOSS.variable_index=Variable(:,in);
BOSS.nVAR=length(Variable(:,in)==1);
BOSS.optPC=CV.optPC;
BOSS.weight=W(:,in);
BOSS.W=W;