#pragma once

// @generated by torchgen/gen.py from NativeFunctions.h

#ifdef TORCH_ASSERT_NO_OPERATORS
#error This change adds a dependency on native_functions.yaml,            \
  meaning the file will need to be re-compiled every time an operator     \
  is changed or added. Consider if your change would be better placed in  \
  another file, or if a more specific header might achieve the same goal. \
  See NOTE: [Tensor vs. TensorBase]
#endif

#if defined(AT_PER_OPERATOR_HEADERS) && defined(TORCH_ASSERT_ONLY_METHOD_OPERATORS)
#error This change adds a dependency on all pytorch operators, meaning the      \
  file will need to be re-compiled every time an operator is changed or added.  \
  Consider including a specific operator from <ATen/ops/{my_operator}_native.h> \
  and see NOTE [TORCH_ASSERT_ONLY_METHOD_OPERATORS].
#endif

#include <c10/core/Scalar.h>
#include <c10/core/Storage.h>
#include <c10/core/TensorOptions.h>
#include <c10/util/Deprecated.h>
#include <optional>
#include <c10/core/QScheme.h>
#include <ATen/core/Reduction.h>
#include <ATen/core/Tensor.h>
#include <tuple>
#include <vector>

#include <ATen/ops/_adaptive_avg_pool2d_native.h>
#include <ATen/ops/_adaptive_avg_pool2d_backward_native.h>
#include <ATen/ops/_adaptive_avg_pool3d_native.h>
#include <ATen/ops/_adaptive_avg_pool3d_backward_native.h>
#include <ATen/ops/_add_batch_dim_native.h>
#include <ATen/ops/_add_relu_native.h>
#include <ATen/ops/_addmm_activation_native.h>
#include <ATen/ops/_aminmax_native.h>
#include <ATen/ops/_amp_foreach_non_finite_check_and_unscale_native.h>
#include <ATen/ops/_amp_update_scale_native.h>
#include <ATen/ops/_assert_async_native.h>
#include <ATen/ops/_assert_scalar_native.h>
#include <ATen/ops/_assert_tensor_metadata_native.h>
#include <ATen/ops/_autocast_to_full_precision_native.h>
#include <ATen/ops/_autocast_to_reduced_precision_native.h>
#include <ATen/ops/_backward_native.h>
#include <ATen/ops/_batch_norm_impl_index_native.h>
#include <ATen/ops/_batch_norm_impl_index_backward_native.h>
#include <ATen/ops/_batch_norm_no_update_native.h>
#include <ATen/ops/_batch_norm_with_update_native.h>
#include <ATen/ops/_cast_Byte_native.h>
#include <ATen/ops/_cast_Char_native.h>
#include <ATen/ops/_cast_Double_native.h>
#include <ATen/ops/_cast_Float_native.h>
#include <ATen/ops/_cast_Half_native.h>
#include <ATen/ops/_cast_Int_native.h>
#include <ATen/ops/_cast_Long_native.h>
#include <ATen/ops/_cast_Short_native.h>
#include <ATen/ops/_cdist_backward_native.h>
#include <ATen/ops/_cdist_forward_native.h>
#include <ATen/ops/_cholesky_solve_helper_native.h>
#include <ATen/ops/_choose_qparams_per_tensor_native.h>
#include <ATen/ops/_chunk_cat_native.h>
#include <ATen/ops/_coalesce_native.h>
#include <ATen/ops/_coalesced_native.h>
#include <ATen/ops/_compute_linear_combination_native.h>
#include <ATen/ops/_conj_native.h>
#include <ATen/ops/_conj_copy_native.h>
#include <ATen/ops/_conj_physical_native.h>
#include <ATen/ops/_conv_depthwise2d_native.h>
#include <ATen/ops/_convert_indices_from_coo_to_csr_native.h>
#include <ATen/ops/_convert_indices_from_csr_to_coo_native.h>
#include <ATen/ops/_convert_weight_to_int4pack_native.h>
#include <ATen/ops/_convert_weight_to_int4pack_for_cpu_native.h>
#include <ATen/ops/_convolution_native.h>
#include <ATen/ops/_convolution_double_backward_native.h>
#include <ATen/ops/_convolution_mode_native.h>
#include <ATen/ops/_copy_from_native.h>
#include <ATen/ops/_copy_from_and_resize_native.h>
#include <ATen/ops/_cslt_compress_native.h>
#include <ATen/ops/_cslt_sparse_mm_native.h>
#include <ATen/ops/_cslt_sparse_mm_search_native.h>
#include <ATen/ops/_ctc_loss_native.h>
#include <ATen/ops/_ctc_loss_backward_native.h>
#include <ATen/ops/_cudnn_attention_forward_native.h>
#include <ATen/ops/_cudnn_ctc_loss_native.h>
#include <ATen/ops/_cudnn_init_dropout_state_native.h>
#include <ATen/ops/_cudnn_rnn_native.h>
#include <ATen/ops/_cudnn_rnn_backward_native.h>
#include <ATen/ops/_cudnn_rnn_flatten_weight_native.h>
#include <ATen/ops/_cufft_clear_plan_cache_native.h>
#include <ATen/ops/_cufft_get_plan_cache_max_size_native.h>
#include <ATen/ops/_cufft_get_plan_cache_size_native.h>
#include <ATen/ops/_cufft_set_plan_cache_max_size_native.h>
#include <ATen/ops/_cummax_helper_native.h>
#include <ATen/ops/_cummin_helper_native.h>
#include <ATen/ops/_debug_has_internal_overlap_native.h>
#include <ATen/ops/_dimI_native.h>
#include <ATen/ops/_dimV_native.h>
#include <ATen/ops/_dim_arange_native.h>
#include <ATen/ops/_dirichlet_grad_native.h>
#include <ATen/ops/_dyn_quant_matmul_4bit_native.h>
#include <ATen/ops/_dyn_quant_pack_4bit_weight_native.h>
#include <ATen/ops/_efficient_attention_backward_native.h>
#include <ATen/ops/_efficient_attention_forward_native.h>
#include <ATen/ops/_efficientzerotensor_native.h>
#include <ATen/ops/_embedding_bag_native.h>
#include <ATen/ops/_embedding_bag_backward_native.h>
#include <ATen/ops/_embedding_bag_dense_backward_native.h>
#include <ATen/ops/_embedding_bag_forward_only_native.h>
#include <ATen/ops/_embedding_bag_per_sample_weights_backward_native.h>
#include <ATen/ops/_embedding_bag_sparse_backward_native.h>
#include <ATen/ops/_empty_affine_quantized_native.h>
#include <ATen/ops/_empty_per_channel_affine_quantized_native.h>
#include <ATen/ops/_euclidean_dist_native.h>
#include <ATen/ops/_fake_quantize_learnable_per_channel_affine_native.h>
#include <ATen/ops/_fake_quantize_learnable_per_channel_affine_backward_native.h>
#include <ATen/ops/_fake_quantize_learnable_per_tensor_affine_native.h>
#include <ATen/ops/_fake_quantize_learnable_per_tensor_affine_backward_native.h>
#include <ATen/ops/_fake_quantize_per_tensor_affine_cachemask_tensor_qparams_native.h>
#include <ATen/ops/_fft_c2c_native.h>
#include <ATen/ops/_fft_c2r_native.h>
#include <ATen/ops/_fft_r2c_native.h>
#include <ATen/ops/_fill_mem_eff_dropout_mask_native.h>
#include <ATen/ops/_flash_attention_backward_native.h>
#include <ATen/ops/_flash_attention_forward_native.h>
#include <ATen/ops/_foobar_native.h>
#include <ATen/ops/_foreach_abs_native.h>
#include <ATen/ops/_foreach_acos_native.h>
#include <ATen/ops/_foreach_add_native.h>
#include <ATen/ops/_foreach_addcdiv_native.h>
#include <ATen/ops/_foreach_addcmul_native.h>
#include <ATen/ops/_foreach_asin_native.h>
#include <ATen/ops/_foreach_atan_native.h>
#include <ATen/ops/_foreach_ceil_native.h>
#include <ATen/ops/_foreach_clamp_max_native.h>
#include <ATen/ops/_foreach_clamp_min_native.h>
#include <ATen/ops/_foreach_copy_native.h>
#include <ATen/ops/_foreach_cos_native.h>
#include <ATen/ops/_foreach_cosh_native.h>
#include <ATen/ops/_foreach_div_native.h>
#include <ATen/ops/_foreach_erf_native.h>
#include <ATen/ops/_foreach_erfc_native.h>
#include <ATen/ops/_foreach_exp_native.h>
#include <ATen/ops/_foreach_expm1_native.h>
#include <ATen/ops/_foreach_floor_native.h>
#include <ATen/ops/_foreach_frac_native.h>
#include <ATen/ops/_foreach_lerp_native.h>
#include <ATen/ops/_foreach_lgamma_native.h>
#include <ATen/ops/_foreach_log_native.h>
#include <ATen/ops/_foreach_log10_native.h>
#include <ATen/ops/_foreach_log1p_native.h>
#include <ATen/ops/_foreach_log2_native.h>
#include <ATen/ops/_foreach_max_native.h>
#include <ATen/ops/_foreach_maximum_native.h>
#include <ATen/ops/_foreach_minimum_native.h>
#include <ATen/ops/_foreach_mul_native.h>
#include <ATen/ops/_foreach_neg_native.h>
#include <ATen/ops/_foreach_norm_native.h>
#include <ATen/ops/_foreach_pow_native.h>
#include <ATen/ops/_foreach_reciprocal_native.h>
#include <ATen/ops/_foreach_round_native.h>
#include <ATen/ops/_foreach_rsqrt_native.h>
#include <ATen/ops/_foreach_sigmoid_native.h>
#include <ATen/ops/_foreach_sign_native.h>
#include <ATen/ops/_foreach_sin_native.h>
#include <ATen/ops/_foreach_sinh_native.h>
#include <ATen/ops/_foreach_sqrt_native.h>
#include <ATen/ops/_foreach_sub_native.h>
#include <ATen/ops/_foreach_tan_native.h>
#include <ATen/ops/_foreach_tanh_native.h>
#include <ATen/ops/_foreach_trunc_native.h>
#include <ATen/ops/_foreach_zero_native.h>
#include <ATen/ops/_functional_assert_async_native.h>
#include <ATen/ops/_functional_assert_scalar_native.h>
#include <ATen/ops/_functional_sym_constrain_range_native.h>
#include <ATen/ops/_functional_sym_constrain_range_for_size_native.h>
#include <ATen/ops/_fused_adagrad_native.h>
#include <ATen/ops/_fused_adam_native.h>
#include <ATen/ops/_fused_adamw_native.h>
#include <ATen/ops/_fused_dropout_native.h>
#include <ATen/ops/_fused_moving_avg_obs_fq_helper_native.h>
#include <ATen/ops/_fused_sdp_choice_native.h>
#include <ATen/ops/_fused_sgd_native.h>
#include <ATen/ops/_fw_primal_native.h>
#include <ATen/ops/_fw_primal_copy_native.h>
#include <ATen/ops/_gather_sparse_backward_native.h>
#include <ATen/ops/_grid_sampler_2d_cpu_fallback_native.h>
#include <ATen/ops/_grid_sampler_2d_cpu_fallback_backward_native.h>
#include <ATen/ops/_has_compatible_shallow_copy_type_native.h>
#include <ATen/ops/_has_same_storage_numel_native.h>
#include <ATen/ops/_histogramdd_bin_edges_native.h>
#include <ATen/ops/_histogramdd_from_bin_cts_native.h>
#include <ATen/ops/_histogramdd_from_bin_tensors_native.h>
#include <ATen/ops/_index_put_impl_native.h>
#include <ATen/ops/_indices_native.h>
#include <ATen/ops/_indices_copy_native.h>
#include <ATen/ops/_int_mm_native.h>
#include <ATen/ops/_is_all_true_native.h>
#include <ATen/ops/_is_any_true_native.h>
#include <ATen/ops/_is_zerotensor_native.h>
#include <ATen/ops/_jagged_to_padded_dense_forward_native.h>
#include <ATen/ops/_lazy_clone_native.h>
#include <ATen/ops/_linalg_check_errors_native.h>
#include <ATen/ops/_linalg_det_native.h>
#include <ATen/ops/_linalg_eigh_native.h>
#include <ATen/ops/_linalg_eigvals_native.h>
#include <ATen/ops/_linalg_slogdet_native.h>
#include <ATen/ops/_linalg_solve_ex_native.h>
#include <ATen/ops/_linalg_svd_native.h>
#include <ATen/ops/_local_scalar_dense_native.h>
#include <ATen/ops/_log_softmax_native.h>
#include <ATen/ops/_log_softmax_backward_data_native.h>
#include <ATen/ops/_logcumsumexp_native.h>
#include <ATen/ops/_lstm_mps_native.h>
#include <ATen/ops/_lu_with_info_native.h>
#include <ATen/ops/_make_dep_token_native.h>
#include <ATen/ops/_make_dual_native.h>
#include <ATen/ops/_make_dual_copy_native.h>
#include <ATen/ops/_make_per_channel_quantized_tensor_native.h>
#include <ATen/ops/_make_per_tensor_quantized_tensor_native.h>
#include <ATen/ops/_masked_scale_native.h>
#include <ATen/ops/_masked_softmax_native.h>
#include <ATen/ops/_masked_softmax_backward_native.h>
#include <ATen/ops/_mixed_dtypes_linear_native.h>
#include <ATen/ops/_mkldnn_reshape_native.h>
#include <ATen/ops/_mkldnn_transpose_native.h>
#include <ATen/ops/_mps_convolution_native.h>
#include <ATen/ops/_mps_convolution_transpose_native.h>
#include <ATen/ops/_native_batch_norm_legit_native.h>
#include <ATen/ops/_native_batch_norm_legit_no_training_native.h>
#include <ATen/ops/_native_multi_head_attention_native.h>
#include <ATen/ops/_neg_view_native.h>
#include <ATen/ops/_neg_view_copy_native.h>
#include <ATen/ops/_nested_compute_contiguous_strides_offsets_native.h>
#include <ATen/ops/_nested_from_padded_native.h>
#include <ATen/ops/_nested_from_padded_and_nested_example_native.h>
#include <ATen/ops/_nested_from_padded_tensor_native.h>
#include <ATen/ops/_nested_get_jagged_dummy_native.h>
#include <ATen/ops/_nested_get_lengths_native.h>
#include <ATen/ops/_nested_get_max_seqlen_native.h>
#include <ATen/ops/_nested_get_min_seqlen_native.h>
#include <ATen/ops/_nested_get_offsets_native.h>
#include <ATen/ops/_nested_get_ragged_idx_native.h>
#include <ATen/ops/_nested_get_values_native.h>
#include <ATen/ops/_nested_get_values_copy_native.h>
#include <ATen/ops/_nested_select_backward_native.h>
#include <ATen/ops/_nested_sum_backward_native.h>
#include <ATen/ops/_nested_tensor_from_mask_native.h>
#include <ATen/ops/_nested_tensor_from_mask_left_aligned_native.h>
#include <ATen/ops/_nested_tensor_from_tensor_list_native.h>
#include <ATen/ops/_nested_tensor_size_native.h>
#include <ATen/ops/_nested_tensor_softmax_with_shape_native.h>
#include <ATen/ops/_nested_tensor_storage_offsets_native.h>
#include <ATen/ops/_nested_tensor_strides_native.h>
#include <ATen/ops/_nested_view_from_buffer_native.h>
#include <ATen/ops/_nested_view_from_buffer_copy_native.h>
#include <ATen/ops/_nested_view_from_jagged_native.h>
#include <ATen/ops/_nested_view_from_jagged_copy_native.h>
#include <ATen/ops/_new_zeros_with_same_feature_meta_native.h>
#include <ATen/ops/_nnpack_available_native.h>
#include <ATen/ops/_nnpack_spatial_convolution_native.h>
#include <ATen/ops/_nnz_native.h>
#include <ATen/ops/_pack_padded_sequence_native.h>
#include <ATen/ops/_pack_padded_sequence_backward_native.h>
#include <ATen/ops/_pad_circular_native.h>
#include <ATen/ops/_pad_enum_native.h>
#include <ATen/ops/_pad_packed_sequence_native.h>
#include <ATen/ops/_padded_dense_to_jagged_forward_native.h>
#include <ATen/ops/_pdist_backward_native.h>
#include <ATen/ops/_pdist_forward_native.h>
#include <ATen/ops/_pin_memory_native.h>
#include <ATen/ops/_prelu_kernel_native.h>
#include <ATen/ops/_prelu_kernel_backward_native.h>
#include <ATen/ops/_print_native.h>
#include <ATen/ops/_propagate_xla_data_native.h>
#include <ATen/ops/_remove_batch_dim_native.h>
#include <ATen/ops/_reshape_alias_native.h>
#include <ATen/ops/_reshape_alias_copy_native.h>
#include <ATen/ops/_reshape_copy_native.h>
#include <ATen/ops/_reshape_from_tensor_native.h>
#include <ATen/ops/_resize_output_native.h>
#include <ATen/ops/_rowwise_prune_native.h>
#include <ATen/ops/_safe_softmax_native.h>
#include <ATen/ops/_sample_dirichlet_native.h>
#include <ATen/ops/_saturate_weight_to_fp16_native.h>
#include <ATen/ops/_scaled_dot_product_attention_math_native.h>
#include <ATen/ops/_scaled_dot_product_attention_math_for_mps_native.h>
#include <ATen/ops/_scaled_dot_product_cudnn_attention_native.h>
#include <ATen/ops/_scaled_dot_product_cudnn_attention_backward_native.h>
#include <ATen/ops/_scaled_dot_product_efficient_attention_native.h>
#include <ATen/ops/_scaled_dot_product_efficient_attention_backward_native.h>
#include <ATen/ops/_scaled_dot_product_flash_attention_native.h>
#include <ATen/ops/_scaled_dot_product_flash_attention_backward_native.h>
#include <ATen/ops/_scaled_dot_product_flash_attention_for_cpu_native.h>
#include <ATen/ops/_scaled_dot_product_flash_attention_for_cpu_backward_native.h>
#include <ATen/ops/_scaled_dot_product_fused_attention_overrideable_native.h>
#include <ATen/ops/_scaled_dot_product_fused_attention_overrideable_backward_native.h>
#include <ATen/ops/_scaled_grouped_mm_native.h>
#include <ATen/ops/_scaled_mm_native.h>
#include <ATen/ops/_segment_reduce_backward_native.h>
#include <ATen/ops/_shape_as_tensor_native.h>
#include <ATen/ops/_slow_conv2d_backward_native.h>
#include <ATen/ops/_slow_conv2d_forward_native.h>
#include <ATen/ops/_sobol_engine_draw_native.h>
#include <ATen/ops/_sobol_engine_ff_native.h>
#include <ATen/ops/_sobol_engine_initialize_state_native.h>
#include <ATen/ops/_sobol_engine_scramble_native.h>
#include <ATen/ops/_softmax_native.h>
#include <ATen/ops/_softmax_backward_data_native.h>
#include <ATen/ops/_sparse_addmm_native.h>
#include <ATen/ops/_sparse_broadcast_to_native.h>
#include <ATen/ops/_sparse_broadcast_to_copy_native.h>
#include <ATen/ops/_sparse_bsc_tensor_unsafe_native.h>
#include <ATen/ops/_sparse_bsr_tensor_unsafe_native.h>
#include <ATen/ops/_sparse_compressed_tensor_unsafe_native.h>
#include <ATen/ops/_sparse_compressed_tensor_with_dims_native.h>
#include <ATen/ops/_sparse_coo_tensor_unsafe_native.h>
#include <ATen/ops/_sparse_coo_tensor_with_dims_native.h>
#include <ATen/ops/_sparse_coo_tensor_with_dims_and_tensors_native.h>
#include <ATen/ops/_sparse_csc_tensor_unsafe_native.h>
#include <ATen/ops/_sparse_csr_prod_native.h>
#include <ATen/ops/_sparse_csr_sum_native.h>
#include <ATen/ops/_sparse_csr_tensor_unsafe_native.h>
#include <ATen/ops/_sparse_log_softmax_native.h>
#include <ATen/ops/_sparse_log_softmax_backward_data_native.h>
#include <ATen/ops/_sparse_mask_projection_native.h>
#include <ATen/ops/_sparse_mm_native.h>
#include <ATen/ops/_sparse_mm_reduce_impl_native.h>
#include <ATen/ops/_sparse_mm_reduce_impl_backward_native.h>
#include <ATen/ops/_sparse_semi_structured_addmm_native.h>
#include <ATen/ops/_sparse_semi_structured_apply_native.h>
#include <ATen/ops/_sparse_semi_structured_apply_dense_native.h>
#include <ATen/ops/_sparse_semi_structured_linear_native.h>
#include <ATen/ops/_sparse_semi_structured_mm_native.h>
#include <ATen/ops/_sparse_semi_structured_tile_native.h>
#include <ATen/ops/_sparse_softmax_native.h>
#include <ATen/ops/_sparse_softmax_backward_data_native.h>
#include <ATen/ops/_sparse_sparse_matmul_native.h>
#include <ATen/ops/_sparse_sum_native.h>
#include <ATen/ops/_sparse_sum_backward_native.h>
#include <ATen/ops/_spdiags_native.h>
#include <ATen/ops/_spsolve_native.h>
#include <ATen/ops/_stack_native.h>
#include <ATen/ops/_standard_gamma_native.h>
#include <ATen/ops/_standard_gamma_grad_native.h>
#include <ATen/ops/_test_ambiguous_defaults_native.h>
#include <ATen/ops/_test_autograd_multiple_dispatch_native.h>
#include <ATen/ops/_test_autograd_multiple_dispatch_view_native.h>
#include <ATen/ops/_test_autograd_multiple_dispatch_view_copy_native.h>
#include <ATen/ops/_test_check_tensor_native.h>
#include <ATen/ops/_test_functorch_fallback_native.h>
#include <ATen/ops/_test_optional_filled_intlist_native.h>
#include <ATen/ops/_test_optional_floatlist_native.h>
#include <ATen/ops/_test_optional_intlist_native.h>
#include <ATen/ops/_test_parallel_materialize_native.h>
#include <ATen/ops/_test_serialization_subcmul_native.h>
#include <ATen/ops/_test_string_default_native.h>
#include <ATen/ops/_test_warn_in_autograd_native.h>
#include <ATen/ops/_thnn_differentiable_gru_cell_backward_native.h>
#include <ATen/ops/_thnn_differentiable_lstm_cell_backward_native.h>
#include <ATen/ops/_thnn_fused_gru_cell_native.h>
#include <ATen/ops/_thnn_fused_gru_cell_backward_native.h>
#include <ATen/ops/_thnn_fused_lstm_cell_native.h>
#include <ATen/ops/_thnn_fused_lstm_cell_backward_native.h>
#include <ATen/ops/_thnn_fused_lstm_cell_backward_impl_native.h>
#include <ATen/ops/_to_copy_native.h>
#include <ATen/ops/_to_cpu_native.h>
#include <ATen/ops/_to_dense_native.h>
#include <ATen/ops/_to_sparse_native.h>
#include <ATen/ops/_to_sparse_bsc_native.h>
#include <ATen/ops/_to_sparse_bsr_native.h>
#include <ATen/ops/_to_sparse_csc_native.h>
#include <ATen/ops/_to_sparse_csr_native.h>
#include <ATen/ops/_to_sparse_semi_structured_native.h>
#include <ATen/ops/_transform_bias_rescale_qkv_native.h>
#include <ATen/ops/_transformer_encoder_layer_fwd_native.h>
#include <ATen/ops/_trilinear_native.h>
#include <ATen/ops/_triton_multi_head_attention_native.h>
#include <ATen/ops/_triton_scaled_dot_attention_native.h>
#include <ATen/ops/_unique_native.h>
#include <ATen/ops/_unique2_native.h>
#include <ATen/ops/_unpack_dual_native.h>
#include <ATen/ops/_unsafe_index_native.h>
#include <ATen/ops/_unsafe_index_put_native.h>
#include <ATen/ops/_unsafe_masked_index_native.h>
#include <ATen/ops/_unsafe_masked_index_put_accumulate_native.h>
#include <ATen/ops/_unsafe_view_native.h>
#include <ATen/ops/_upsample_bicubic2d_aa_native.h>
#include <ATen/ops/_upsample_bicubic2d_aa_backward_native.h>
#include <ATen/ops/_upsample_bilinear2d_aa_native.h>
#include <ATen/ops/_upsample_bilinear2d_aa_backward_native.h>
#include <ATen/ops/_upsample_nearest_exact1d_native.h>
#include <ATen/ops/_upsample_nearest_exact1d_backward_native.h>
#include <ATen/ops/_upsample_nearest_exact2d_native.h>
#include <ATen/ops/_upsample_nearest_exact2d_backward_native.h>
#include <ATen/ops/_upsample_nearest_exact3d_native.h>
#include <ATen/ops/_upsample_nearest_exact3d_backward_native.h>
#include <ATen/ops/_use_cudnn_ctc_loss_native.h>
#include <ATen/ops/_use_cudnn_rnn_flatten_weight_native.h>
#include <ATen/ops/_validate_compressed_sparse_indices_native.h>
#include <ATen/ops/_validate_sparse_bsc_tensor_args_native.h>
#include <ATen/ops/_validate_sparse_bsr_tensor_args_native.h>
#include <ATen/ops/_validate_sparse_compressed_tensor_args_native.h>
#include <ATen/ops/_validate_sparse_coo_tensor_args_native.h>
#include <ATen/ops/_validate_sparse_csc_tensor_args_native.h>
#include <ATen/ops/_validate_sparse_csr_tensor_args_native.h>
#include <ATen/ops/_values_native.h>
#include <ATen/ops/_values_copy_native.h>
#include <ATen/ops/_version_native.h>
#include <ATen/ops/_weight_int4pack_mm_native.h>
#include <ATen/ops/_weight_int4pack_mm_for_cpu_native.h>
#include <ATen/ops/_weight_int8pack_mm_native.h>
#include <ATen/ops/_weight_norm_native.h>
#include <ATen/ops/_weight_norm_differentiable_backward_native.h>
#include <ATen/ops/_weight_norm_interface_native.h>
#include <ATen/ops/_weight_norm_interface_backward_native.h>
#include <ATen/ops/_wrapped_linear_prepack_native.h>
#include <ATen/ops/_wrapped_quantized_linear_prepacked_native.h>
#include <ATen/ops/abs_native.h>
#include <ATen/ops/absolute_native.h>
#include <ATen/ops/acos_native.h>
#include <ATen/ops/acosh_native.h>
#include <ATen/ops/adaptive_avg_pool1d_native.h>
#include <ATen/ops/adaptive_avg_pool2d_native.h>
#include <ATen/ops/adaptive_avg_pool3d_native.h>
#include <ATen/ops/adaptive_avg_pool3d_backward_native.h>
#include <ATen/ops/adaptive_max_pool1d_native.h>
#include <ATen/ops/adaptive_max_pool2d_native.h>
#include <ATen/ops/adaptive_max_pool2d_backward_native.h>
#include <ATen/ops/adaptive_max_pool3d_native.h>
#include <ATen/ops/adaptive_max_pool3d_backward_native.h>
#include <ATen/ops/add_native.h>
#include <ATen/ops/addbmm_native.h>
#include <ATen/ops/addcdiv_native.h>
#include <ATen/ops/addcmul_native.h>
#include <ATen/ops/addmm_native.h>
#include <ATen/ops/addmv_native.h>
#include <ATen/ops/addr_native.h>
#include <ATen/ops/adjoint_native.h>
#include <ATen/ops/affine_grid_generator_native.h>
#include <ATen/ops/affine_grid_generator_backward_native.h>
#include <ATen/ops/alias_native.h>
#include <ATen/ops/alias_copy_native.h>
#include <ATen/ops/align_as_native.h>
#include <ATen/ops/align_tensors_native.h>
#include <ATen/ops/align_to_native.h>
#include <ATen/ops/all_native.h>
#include <ATen/ops/allclose_native.h>
#include <ATen/ops/alpha_dropout_native.h>
#include <ATen/ops/amax_native.h>
#include <ATen/ops/amin_native.h>
#include <ATen/ops/aminmax_native.h>
#include <ATen/ops/and_native.h>
#include <ATen/ops/angle_native.h>
#include <ATen/ops/any_native.h>
#include <ATen/ops/arange_native.h>
#include <ATen/ops/arccos_native.h>
#include <ATen/ops/arccosh_native.h>
#include <ATen/ops/arcsin_native.h>
#include <ATen/ops/arcsinh_native.h>
#include <ATen/ops/arctan_native.h>
#include <ATen/ops/arctan2_native.h>
#include <ATen/ops/arctanh_native.h>
#include <ATen/ops/argmax_native.h>
#include <ATen/ops/argmin_native.h>
#include <ATen/ops/argsort_native.h>
#include <ATen/ops/argwhere_native.h>
#include <ATen/ops/as_strided_native.h>
#include <ATen/ops/as_strided_copy_native.h>
#include <ATen/ops/as_strided_scatter_native.h>
#include <ATen/ops/asin_native.h>
#include <ATen/ops/asinh_native.h>
#include <ATen/ops/atan_native.h>
#include <ATen/ops/atan2_native.h>
#include <ATen/ops/atanh_native.h>
#include <ATen/ops/atleast_1d_native.h>
#include <ATen/ops/atleast_2d_native.h>
#include <ATen/ops/atleast_3d_native.h>
#include <ATen/ops/avg_pool1d_native.h>
#include <ATen/ops/avg_pool2d_native.h>
#include <ATen/ops/avg_pool2d_backward_native.h>
#include <ATen/ops/avg_pool3d_native.h>
#include <ATen/ops/avg_pool3d_backward_native.h>
#include <ATen/ops/baddbmm_native.h>
#include <ATen/ops/bartlett_window_native.h>
#include <ATen/ops/batch_norm_native.h>
#include <ATen/ops/batch_norm_backward_native.h>
#include <ATen/ops/batch_norm_backward_elemt_native.h>
#include <ATen/ops/batch_norm_backward_reduce_native.h>
#include <ATen/ops/batch_norm_elemt_native.h>
#include <ATen/ops/batch_norm_gather_stats_native.h>
#include <ATen/ops/batch_norm_gather_stats_with_counts_native.h>
#include <ATen/ops/batch_norm_stats_native.h>
#include <ATen/ops/batch_norm_update_stats_native.h>
#include <ATen/ops/bernoulli_native.h>
#include <ATen/ops/bilinear_native.h>
#include <ATen/ops/binary_cross_entropy_native.h>
#include <ATen/ops/binary_cross_entropy_backward_native.h>
#include <ATen/ops/binary_cross_entropy_with_logits_native.h>
#include <ATen/ops/bincount_native.h>
#include <ATen/ops/binomial_native.h>
#include <ATen/ops/bitwise_and_native.h>
#include <ATen/ops/bitwise_left_shift_native.h>
#include <ATen/ops/bitwise_not_native.h>
#include <ATen/ops/bitwise_or_native.h>
#include <ATen/ops/bitwise_right_shift_native.h>
#include <ATen/ops/bitwise_xor_native.h>
#include <ATen/ops/blackman_window_native.h>
#include <ATen/ops/block_diag_native.h>
#include <ATen/ops/bmm_native.h>
#include <ATen/ops/broadcast_tensors_native.h>
#include <ATen/ops/broadcast_to_native.h>
#include <ATen/ops/bucketize_native.h>
#include <ATen/ops/can_cast_native.h>
#include <ATen/ops/cartesian_prod_native.h>
#include <ATen/ops/cat_native.h>
#include <ATen/ops/cauchy_native.h>
#include <ATen/ops/ccol_indices_native.h>
#include <ATen/ops/ccol_indices_copy_native.h>
#include <ATen/ops/cdist_native.h>
#include <ATen/ops/ceil_native.h>
#include <ATen/ops/celu_native.h>
#include <ATen/ops/chain_matmul_native.h>
#include <ATen/ops/chalf_native.h>
#include <ATen/ops/channel_shuffle_native.h>
#include <ATen/ops/cholesky_native.h>
#include <ATen/ops/cholesky_inverse_native.h>
#include <ATen/ops/cholesky_solve_native.h>
#include <ATen/ops/choose_qparams_optimized_native.h>
#include <ATen/ops/chunk_native.h>
#include <ATen/ops/clamp_native.h>
#include <ATen/ops/clamp_max_native.h>
#include <ATen/ops/clamp_min_native.h>
#include <ATen/ops/clip_native.h>
#include <ATen/ops/clone_native.h>
#include <ATen/ops/coalesce_native.h>
#include <ATen/ops/col2im_native.h>
#include <ATen/ops/col_indices_native.h>
#include <ATen/ops/col_indices_copy_native.h>
#include <ATen/ops/column_stack_native.h>
#include <ATen/ops/combinations_native.h>
#include <ATen/ops/complex_native.h>
#include <ATen/ops/concat_native.h>
#include <ATen/ops/concatenate_native.h>
#include <ATen/ops/conj_native.h>
#include <ATen/ops/conj_physical_native.h>
#include <ATen/ops/constant_pad_nd_native.h>
#include <ATen/ops/contiguous_native.h>
#include <ATen/ops/conv1d_native.h>
#include <ATen/ops/conv2d_native.h>
#include <ATen/ops/conv3d_native.h>
#include <ATen/ops/conv_depthwise3d_native.h>
#include <ATen/ops/conv_tbc_native.h>
#include <ATen/ops/conv_tbc_backward_native.h>
#include <ATen/ops/conv_transpose1d_native.h>
#include <ATen/ops/conv_transpose2d_native.h>
#include <ATen/ops/conv_transpose3d_native.h>
#include <ATen/ops/convolution_native.h>
#include <ATen/ops/convolution_backward_native.h>
#include <ATen/ops/convolution_backward_overrideable_native.h>
#include <ATen/ops/convolution_overrideable_native.h>
#include <ATen/ops/copy_native.h>
#include <ATen/ops/copy_sparse_to_sparse_native.h>
#include <ATen/ops/copysign_native.h>
#include <ATen/ops/corrcoef_native.h>
#include <ATen/ops/cos_native.h>
#include <ATen/ops/cosh_native.h>
#include <ATen/ops/cosine_embedding_loss_native.h>
#include <ATen/ops/cosine_similarity_native.h>
#include <ATen/ops/count_nonzero_native.h>
#include <ATen/ops/cov_native.h>
#include <ATen/ops/cross_native.h>
#include <ATen/ops/cross_entropy_loss_native.h>
#include <ATen/ops/crow_indices_native.h>
#include <ATen/ops/crow_indices_copy_native.h>
#include <ATen/ops/ctc_loss_native.h>
#include <ATen/ops/cudnn_affine_grid_generator_native.h>
#include <ATen/ops/cudnn_affine_grid_generator_backward_native.h>
#include <ATen/ops/cudnn_batch_norm_native.h>
#include <ATen/ops/cudnn_batch_norm_backward_native.h>
#include <ATen/ops/cudnn_convolution_native.h>
#include <ATen/ops/cudnn_convolution_add_relu_native.h>
#include <ATen/ops/cudnn_convolution_relu_native.h>
#include <ATen/ops/cudnn_convolution_transpose_native.h>
#include <ATen/ops/cudnn_grid_sampler_native.h>
#include <ATen/ops/cudnn_grid_sampler_backward_native.h>
#include <ATen/ops/cudnn_is_acceptable_native.h>
#include <ATen/ops/cummax_native.h>
#include <ATen/ops/cummaxmin_backward_native.h>
#include <ATen/ops/cummin_native.h>
#include <ATen/ops/cumprod_native.h>
#include <ATen/ops/cumprod_backward_native.h>
#include <ATen/ops/cumsum_native.h>
#include <ATen/ops/cumulative_trapezoid_native.h>
#include <ATen/ops/data_native.h>
#include <ATen/ops/deg2rad_native.h>
#include <ATen/ops/dense_dim_native.h>
#include <ATen/ops/dequantize_native.h>
#include <ATen/ops/det_native.h>
#include <ATen/ops/detach_native.h>
#include <ATen/ops/detach_copy_native.h>
#include <ATen/ops/diag_native.h>
#include <ATen/ops/diag_embed_native.h>
#include <ATen/ops/diagflat_native.h>
#include <ATen/ops/diagonal_native.h>
#include <ATen/ops/diagonal_backward_native.h>
#include <ATen/ops/diagonal_copy_native.h>
#include <ATen/ops/diagonal_scatter_native.h>
#include <ATen/ops/diff_native.h>
#include <ATen/ops/digamma_native.h>
#include <ATen/ops/dist_native.h>
#include <ATen/ops/div_native.h>
#include <ATen/ops/divide_native.h>
#include <ATen/ops/dot_native.h>
#include <ATen/ops/dropout_native.h>
#include <ATen/ops/dsplit_native.h>
#include <ATen/ops/dstack_native.h>
#include <ATen/ops/einsum_native.h>
#include <ATen/ops/elu_native.h>
#include <ATen/ops/elu_backward_native.h>
#include <ATen/ops/embedding_native.h>
#include <ATen/ops/embedding_backward_native.h>
#include <ATen/ops/embedding_bag_native.h>
#include <ATen/ops/embedding_dense_backward_native.h>
#include <ATen/ops/embedding_renorm_native.h>
#include <ATen/ops/embedding_sparse_backward_native.h>
#include <ATen/ops/empty_native.h>
#include <ATen/ops/empty_like_native.h>
#include <ATen/ops/empty_permuted_native.h>
#include <ATen/ops/empty_quantized_native.h>
#include <ATen/ops/empty_strided_native.h>
#include <ATen/ops/eq_native.h>
#include <ATen/ops/equal_native.h>
#include <ATen/ops/erf_native.h>
#include <ATen/ops/erfc_native.h>
#include <ATen/ops/erfinv_native.h>
#include <ATen/ops/exp_native.h>
#include <ATen/ops/exp2_native.h>
#include <ATen/ops/expand_native.h>
#include <ATen/ops/expand_as_native.h>
#include <ATen/ops/expand_copy_native.h>
#include <ATen/ops/expm1_native.h>
#include <ATen/ops/exponential_native.h>
#include <ATen/ops/eye_native.h>
#include <ATen/ops/fake_quantize_per_channel_affine_native.h>
#include <ATen/ops/fake_quantize_per_channel_affine_cachemask_native.h>
#include <ATen/ops/fake_quantize_per_channel_affine_cachemask_backward_native.h>
#include <ATen/ops/fake_quantize_per_tensor_affine_native.h>
#include <ATen/ops/fake_quantize_per_tensor_affine_cachemask_native.h>
#include <ATen/ops/fake_quantize_per_tensor_affine_cachemask_backward_native.h>
#include <ATen/ops/fbgemm_linear_fp16_weight_native.h>
#include <ATen/ops/fbgemm_linear_fp16_weight_fp32_activation_native.h>
#include <ATen/ops/fbgemm_linear_int8_weight_native.h>
#include <ATen/ops/fbgemm_linear_int8_weight_fp32_activation_native.h>
#include <ATen/ops/fbgemm_linear_quantize_weight_native.h>
#include <ATen/ops/fbgemm_pack_gemm_matrix_fp16_native.h>
#include <ATen/ops/fbgemm_pack_quantized_matrix_native.h>
#include <ATen/ops/feature_alpha_dropout_native.h>
#include <ATen/ops/feature_dropout_native.h>
#include <ATen/ops/fft_fft_native.h>
#include <ATen/ops/fft_fft2_native.h>
#include <ATen/ops/fft_fftfreq_native.h>
#include <ATen/ops/fft_fftn_native.h>
#include <ATen/ops/fft_fftshift_native.h>
#include <ATen/ops/fft_hfft_native.h>
#include <ATen/ops/fft_hfft2_native.h>
#include <ATen/ops/fft_hfftn_native.h>
#include <ATen/ops/fft_ifft_native.h>
#include <ATen/ops/fft_ifft2_native.h>
#include <ATen/ops/fft_ifftn_native.h>
#include <ATen/ops/fft_ifftshift_native.h>
#include <ATen/ops/fft_ihfft_native.h>
#include <ATen/ops/fft_ihfft2_native.h>
#include <ATen/ops/fft_ihfftn_native.h>
#include <ATen/ops/fft_irfft_native.h>
#include <ATen/ops/fft_irfft2_native.h>
#include <ATen/ops/fft_irfftn_native.h>
#include <ATen/ops/fft_rfft_native.h>
#include <ATen/ops/fft_rfft2_native.h>
#include <ATen/ops/fft_rfftfreq_native.h>
#include <ATen/ops/fft_rfftn_native.h>
#include <ATen/ops/fill_native.h>
#include <ATen/ops/fill_diagonal_native.h>
#include <ATen/ops/fix_native.h>
#include <ATen/ops/flatten_native.h>
#include <ATen/ops/flatten_dense_tensors_native.h>
#include <ATen/ops/flip_native.h>
#include <ATen/ops/fliplr_native.h>
#include <ATen/ops/flipud_native.h>
#include <ATen/ops/float_power_native.h>
#include <ATen/ops/floor_native.h>
#include <ATen/ops/floor_divide_native.h>
#include <ATen/ops/fmax_native.h>
#include <ATen/ops/fmin_native.h>
#include <ATen/ops/fmod_native.h>
#include <ATen/ops/frac_native.h>
#include <ATen/ops/fractional_max_pool2d_native.h>
#include <ATen/ops/fractional_max_pool2d_backward_native.h>
#include <ATen/ops/fractional_max_pool3d_native.h>
#include <ATen/ops/fractional_max_pool3d_backward_native.h>
#include <ATen/ops/frexp_native.h>
#include <ATen/ops/frobenius_norm_native.h>
#include <ATen/ops/from_file_native.h>
#include <ATen/ops/full_native.h>
#include <ATen/ops/full_like_native.h>
#include <ATen/ops/fused_moving_avg_obs_fake_quant_native.h>
#include <ATen/ops/gather_native.h>
#include <ATen/ops/gather_backward_native.h>
#include <ATen/ops/gcd_native.h>
#include <ATen/ops/ge_native.h>
#include <ATen/ops/gelu_native.h>
#include <ATen/ops/gelu_backward_native.h>
#include <ATen/ops/geometric_native.h>
#include <ATen/ops/geqrf_native.h>
#include <ATen/ops/ger_native.h>
#include <ATen/ops/glu_native.h>
#include <ATen/ops/glu_backward_native.h>
#include <ATen/ops/glu_backward_jvp_native.h>
#include <ATen/ops/glu_jvp_native.h>
#include <ATen/ops/gradient_native.h>
#include <ATen/ops/greater_native.h>
#include <ATen/ops/greater_equal_native.h>
#include <ATen/ops/grid_sampler_native.h>
#include <ATen/ops/grid_sampler_2d_native.h>
#include <ATen/ops/grid_sampler_2d_backward_native.h>
#include <ATen/ops/grid_sampler_3d_native.h>
#include <ATen/ops/grid_sampler_3d_backward_native.h>
#include <ATen/ops/group_norm_native.h>
#include <ATen/ops/gru_native.h>
#include <ATen/ops/gru_cell_native.h>
#include <ATen/ops/gt_native.h>
#include <ATen/ops/hamming_window_native.h>
#include <ATen/ops/hann_window_native.h>
#include <ATen/ops/hardshrink_native.h>
#include <ATen/ops/hardshrink_backward_native.h>
#include <ATen/ops/hardsigmoid_native.h>
#include <ATen/ops/hardsigmoid_backward_native.h>
#include <ATen/ops/hardswish_native.h>
#include <ATen/ops/hardswish_backward_native.h>
#include <ATen/ops/hardtanh_native.h>
#include <ATen/ops/hardtanh_backward_native.h>
#include <ATen/ops/heaviside_native.h>
#include <ATen/ops/hinge_embedding_loss_native.h>
#include <ATen/ops/histc_native.h>
#include <ATen/ops/histogram_native.h>
#include <ATen/ops/histogramdd_native.h>
#include <ATen/ops/hsplit_native.h>
#include <ATen/ops/hspmm_native.h>
#include <ATen/ops/hstack_native.h>
#include <ATen/ops/huber_loss_native.h>
#include <ATen/ops/huber_loss_backward_native.h>
#include <ATen/ops/hypot_native.h>
#include <ATen/ops/i0_native.h>
#include <ATen/ops/igamma_native.h>
#include <ATen/ops/igammac_native.h>
#include <ATen/ops/im2col_native.h>
#include <ATen/ops/imag_native.h>
#include <ATen/ops/index_native.h>
#include <ATen/ops/index_add_native.h>
#include <ATen/ops/index_copy_native.h>
#include <ATen/ops/index_fill_native.h>
#include <ATen/ops/index_put_native.h>
#include <ATen/ops/index_reduce_native.h>
#include <ATen/ops/index_select_native.h>
#include <ATen/ops/index_select_backward_native.h>
#include <ATen/ops/indices_native.h>
#include <ATen/ops/indices_copy_native.h>
#include <ATen/ops/infinitely_differentiable_gelu_backward_native.h>
#include <ATen/ops/inner_native.h>
#include <ATen/ops/instance_norm_native.h>
#include <ATen/ops/int_repr_native.h>
#include <ATen/ops/inverse_native.h>
#include <ATen/ops/is_coalesced_native.h>
#include <ATen/ops/is_complex_native.h>
#include <ATen/ops/is_conj_native.h>
#include <ATen/ops/is_distributed_native.h>
#include <ATen/ops/is_floating_point_native.h>
#include <ATen/ops/is_inference_native.h>
#include <ATen/ops/is_leaf_native.h>
#include <ATen/ops/is_neg_native.h>
#include <ATen/ops/is_nonzero_native.h>
#include <ATen/ops/is_pinned_native.h>
#include <ATen/ops/is_same_size_native.h>
#include <ATen/ops/is_set_to_native.h>
#include <ATen/ops/is_signed_native.h>
#include <ATen/ops/is_vulkan_available_native.h>
#include <ATen/ops/isclose_native.h>
#include <ATen/ops/isfinite_native.h>
#include <ATen/ops/isin_native.h>
#include <ATen/ops/isinf_native.h>
#include <ATen/ops/isnan_native.h>
#include <ATen/ops/isneginf_native.h>
#include <ATen/ops/isposinf_native.h>
#include <ATen/ops/isreal_native.h>
#include <ATen/ops/istft_native.h>
#include <ATen/ops/item_native.h>
#include <ATen/ops/kaiser_window_native.h>
#include <ATen/ops/kl_div_native.h>
#include <ATen/ops/kron_native.h>
#include <ATen/ops/kthvalue_native.h>
#include <ATen/ops/l1_loss_native.h>
#include <ATen/ops/layer_norm_native.h>
#include <ATen/ops/lcm_native.h>
#include <ATen/ops/ldexp_native.h>
#include <ATen/ops/le_native.h>
#include <ATen/ops/leaky_relu_native.h>
#include <ATen/ops/leaky_relu_backward_native.h>
#include <ATen/ops/lerp_native.h>
#include <ATen/ops/less_native.h>
#include <ATen/ops/less_equal_native.h>
#include <ATen/ops/lgamma_native.h>
#include <ATen/ops/lift_native.h>
#include <ATen/ops/lift_fresh_native.h>
#include <ATen/ops/lift_fresh_copy_native.h>
#include <ATen/ops/linalg_cholesky_native.h>
#include <ATen/ops/linalg_cholesky_ex_native.h>
#include <ATen/ops/linalg_cond_native.h>
#include <ATen/ops/linalg_cross_native.h>
#include <ATen/ops/linalg_det_native.h>
#include <ATen/ops/linalg_diagonal_native.h>
#include <ATen/ops/linalg_eig_native.h>
#include <ATen/ops/linalg_eigh_native.h>
#include <ATen/ops/linalg_eigvals_native.h>
#include <ATen/ops/linalg_eigvalsh_native.h>
#include <ATen/ops/linalg_householder_product_native.h>
#include <ATen/ops/linalg_inv_native.h>
#include <ATen/ops/linalg_inv_ex_native.h>
#include <ATen/ops/linalg_ldl_factor_native.h>
#include <ATen/ops/linalg_ldl_factor_ex_native.h>
#include <ATen/ops/linalg_ldl_solve_native.h>
#include <ATen/ops/linalg_lstsq_native.h>
#include <ATen/ops/linalg_lu_native.h>
#include <ATen/ops/linalg_lu_factor_native.h>
#include <ATen/ops/linalg_lu_factor_ex_native.h>
#include <ATen/ops/linalg_lu_solve_native.h>
#include <ATen/ops/linalg_matmul_native.h>
#include <ATen/ops/linalg_matrix_exp_native.h>
#include <ATen/ops/linalg_matrix_norm_native.h>
#include <ATen/ops/linalg_matrix_power_native.h>
#include <ATen/ops/linalg_matrix_rank_native.h>
#include <ATen/ops/linalg_multi_dot_native.h>
#include <ATen/ops/linalg_norm_native.h>
#include <ATen/ops/linalg_pinv_native.h>
#include <ATen/ops/linalg_qr_native.h>
#include <ATen/ops/linalg_slogdet_native.h>
#include <ATen/ops/linalg_solve_native.h>
#include <ATen/ops/linalg_solve_ex_native.h>
#include <ATen/ops/linalg_solve_triangular_native.h>
#include <ATen/ops/linalg_svd_native.h>
#include <ATen/ops/linalg_svdvals_native.h>
#include <ATen/ops/linalg_tensorinv_native.h>
#include <ATen/ops/linalg_tensorsolve_native.h>
#include <ATen/ops/linalg_vander_native.h>
#include <ATen/ops/linalg_vecdot_native.h>
#include <ATen/ops/linalg_vector_norm_native.h>
#include <ATen/ops/linear_native.h>
#include <ATen/ops/linear_backward_native.h>
#include <ATen/ops/linspace_native.h>
#include <ATen/ops/log_native.h>
#include <ATen/ops/log10_native.h>
#include <ATen/ops/log1p_native.h>
#include <ATen/ops/log2_native.h>
#include <ATen/ops/log_normal_native.h>
#include <ATen/ops/log_sigmoid_native.h>
#include <ATen/ops/log_sigmoid_backward_native.h>
#include <ATen/ops/log_sigmoid_forward_native.h>
#include <ATen/ops/log_softmax_native.h>
#include <ATen/ops/logaddexp_native.h>
#include <ATen/ops/logaddexp2_native.h>
#include <ATen/ops/logcumsumexp_native.h>
#include <ATen/ops/logdet_native.h>
#include <ATen/ops/logical_and_native.h>
#include <ATen/ops/logical_not_native.h>
#include <ATen/ops/logical_or_native.h>
#include <ATen/ops/logical_xor_native.h>
#include <ATen/ops/logit_native.h>
#include <ATen/ops/logit_backward_native.h>
#include <ATen/ops/logspace_native.h>
#include <ATen/ops/logsumexp_native.h>
#include <ATen/ops/lshift_native.h>
#include <ATen/ops/lstm_native.h>
#include <ATen/ops/lstm_cell_native.h>
#include <ATen/ops/lstm_mps_backward_native.h>
#include <ATen/ops/lt_native.h>
#include <ATen/ops/lu_solve_native.h>
#include <ATen/ops/lu_unpack_native.h>
#include <ATen/ops/mH_native.h>
#include <ATen/ops/mT_native.h>
#include <ATen/ops/margin_ranking_loss_native.h>
#include <ATen/ops/masked_fill_native.h>
#include <ATen/ops/masked_scatter_native.h>
#include <ATen/ops/masked_scatter_backward_native.h>
#include <ATen/ops/masked_select_native.h>
#include <ATen/ops/masked_select_backward_native.h>
#include <ATen/ops/matmul_native.h>
#include <ATen/ops/matmul_backward_native.h>
#include <ATen/ops/matrix_H_native.h>
#include <ATen/ops/matrix_exp_native.h>
#include <ATen/ops/matrix_exp_backward_native.h>
#include <ATen/ops/matrix_power_native.h>
#include <ATen/ops/max_native.h>
#include <ATen/ops/max_pool1d_native.h>
#include <ATen/ops/max_pool1d_with_indices_native.h>
#include <ATen/ops/max_pool2d_native.h>
#include <ATen/ops/max_pool2d_backward_native.h>
#include <ATen/ops/max_pool2d_with_indices_native.h>
#include <ATen/ops/max_pool2d_with_indices_backward_native.h>
#include <ATen/ops/max_pool3d_native.h>
#include <ATen/ops/max_pool3d_with_indices_native.h>
#include <ATen/ops/max_pool3d_with_indices_backward_native.h>
#include <ATen/ops/max_unpool2d_native.h>
#include <ATen/ops/max_unpool3d_native.h>
#include <ATen/ops/maximum_native.h>
#include <ATen/ops/mean_native.h>
#include <ATen/ops/median_native.h>
#include <ATen/ops/meshgrid_native.h>
#include <ATen/ops/min_native.h>
#include <ATen/ops/minimum_native.h>
#include <ATen/ops/miopen_batch_norm_native.h>
#include <ATen/ops/miopen_batch_norm_backward_native.h>
#include <ATen/ops/miopen_convolution_native.h>
#include <ATen/ops/miopen_convolution_add_relu_native.h>
#include <ATen/ops/miopen_convolution_relu_native.h>
#include <ATen/ops/miopen_convolution_transpose_native.h>
#include <ATen/ops/miopen_depthwise_convolution_native.h>
#include <ATen/ops/miopen_rnn_native.h>
#include <ATen/ops/miopen_rnn_backward_native.h>
#include <ATen/ops/mish_native.h>
#include <ATen/ops/mish_backward_native.h>
#include <ATen/ops/mkldnn_adaptive_avg_pool2d_native.h>
#include <ATen/ops/mkldnn_adaptive_avg_pool2d_backward_native.h>
#include <ATen/ops/mkldnn_convolution_native.h>
#include <ATen/ops/mkldnn_linear_native.h>
#include <ATen/ops/mkldnn_linear_backward_native.h>
#include <ATen/ops/mkldnn_linear_backward_input_native.h>
#include <ATen/ops/mkldnn_linear_backward_weights_native.h>
#include <ATen/ops/mkldnn_max_pool2d_native.h>
#include <ATen/ops/mkldnn_max_pool2d_backward_native.h>
#include <ATen/ops/mkldnn_max_pool3d_native.h>
#include <ATen/ops/mkldnn_max_pool3d_backward_native.h>
#include <ATen/ops/mkldnn_reorder_conv2d_weight_native.h>
#include <ATen/ops/mkldnn_reorder_conv3d_weight_native.h>
#include <ATen/ops/mkldnn_rnn_layer_native.h>
#include <ATen/ops/mkldnn_rnn_layer_backward_native.h>
#include <ATen/ops/mm_native.h>
#include <ATen/ops/mode_native.h>
#include <ATen/ops/moveaxis_native.h>
#include <ATen/ops/movedim_native.h>
#include <ATen/ops/mps_convolution_backward_native.h>
#include <ATen/ops/mps_convolution_transpose_backward_native.h>
#include <ATen/ops/mse_loss_native.h>
#include <ATen/ops/mse_loss_backward_native.h>
#include <ATen/ops/msort_native.h>
#include <ATen/ops/mul_native.h>
#include <ATen/ops/multi_margin_loss_native.h>
#include <ATen/ops/multi_margin_loss_backward_native.h>
#include <ATen/ops/multilabel_margin_loss_native.h>
#include <ATen/ops/multilabel_margin_loss_backward_native.h>
#include <ATen/ops/multilabel_margin_loss_forward_native.h>
#include <ATen/ops/multinomial_native.h>
#include <ATen/ops/multiply_native.h>
#include <ATen/ops/mv_native.h>
#include <ATen/ops/mvlgamma_native.h>
#include <ATen/ops/nan_to_num_native.h>
#include <ATen/ops/nanmean_native.h>
#include <ATen/ops/nanmedian_native.h>
#include <ATen/ops/nanquantile_native.h>
#include <ATen/ops/nansum_native.h>
#include <ATen/ops/narrow_native.h>
#include <ATen/ops/narrow_copy_native.h>
#include <ATen/ops/native_batch_norm_native.h>
#include <ATen/ops/native_batch_norm_backward_native.h>
#include <ATen/ops/native_channel_shuffle_native.h>
#include <ATen/ops/native_dropout_native.h>
#include <ATen/ops/native_dropout_backward_native.h>
#include <ATen/ops/native_group_norm_native.h>
#include <ATen/ops/native_group_norm_backward_native.h>
#include <ATen/ops/native_layer_norm_native.h>
#include <ATen/ops/native_layer_norm_backward_native.h>
#include <ATen/ops/native_norm_native.h>
#include <ATen/ops/ne_native.h>
#include <ATen/ops/neg_native.h>
#include <ATen/ops/negative_native.h>
#include <ATen/ops/nested_to_padded_tensor_native.h>
#include <ATen/ops/new_empty_native.h>
#include <ATen/ops/new_empty_strided_native.h>
#include <ATen/ops/new_full_native.h>
#include <ATen/ops/new_ones_native.h>
#include <ATen/ops/new_zeros_native.h>
#include <ATen/ops/nextafter_native.h>
#include <ATen/ops/nll_loss_native.h>
#include <ATen/ops/nll_loss2d_native.h>
#include <ATen/ops/nll_loss2d_backward_native.h>
#include <ATen/ops/nll_loss2d_forward_native.h>
#include <ATen/ops/nll_loss_backward_native.h>
#include <ATen/ops/nll_loss_forward_native.h>
#include <ATen/ops/nll_loss_nd_native.h>
#include <ATen/ops/nonzero_native.h>
#include <ATen/ops/nonzero_numpy_native.h>
#include <ATen/ops/nonzero_static_native.h>
#include <ATen/ops/norm_native.h>
#include <ATen/ops/norm_except_dim_native.h>
#include <ATen/ops/normal_native.h>
#include <ATen/ops/not_equal_native.h>
#include <ATen/ops/nuclear_norm_native.h>
#include <ATen/ops/numpy_T_native.h>
#include <ATen/ops/one_hot_native.h>
#include <ATen/ops/ones_native.h>
#include <ATen/ops/ones_like_native.h>
#include <ATen/ops/or_native.h>
#include <ATen/ops/orgqr_native.h>
#include <ATen/ops/ormqr_native.h>
#include <ATen/ops/outer_native.h>
#include <ATen/ops/output_nr_native.h>
#include <ATen/ops/pad_native.h>
#include <ATen/ops/pad_sequence_native.h>
#include <ATen/ops/pairwise_distance_native.h>
#include <ATen/ops/pdist_native.h>
#include <ATen/ops/permute_native.h>
#include <ATen/ops/permute_copy_native.h>
#include <ATen/ops/pin_memory_native.h>
#include <ATen/ops/pinverse_native.h>
#include <ATen/ops/pixel_shuffle_native.h>
#include <ATen/ops/pixel_unshuffle_native.h>
#include <ATen/ops/poisson_native.h>
#include <ATen/ops/poisson_nll_loss_native.h>
#include <ATen/ops/polar_native.h>
#include <ATen/ops/polygamma_native.h>
#include <ATen/ops/positive_native.h>
#include <ATen/ops/pow_native.h>
#include <ATen/ops/prelu_native.h>
#include <ATen/ops/prod_native.h>
#include <ATen/ops/promote_types_native.h>
#include <ATen/ops/put_native.h>
#include <ATen/ops/q_per_channel_axis_native.h>
#include <ATen/ops/q_per_channel_scales_native.h>
#include <ATen/ops/q_per_channel_zero_points_native.h>
#include <ATen/ops/q_scale_native.h>
#include <ATen/ops/q_zero_point_native.h>
#include <ATen/ops/qr_native.h>
#include <ATen/ops/qscheme_native.h>
#include <ATen/ops/quantile_native.h>
#include <ATen/ops/quantize_per_channel_native.h>
#include <ATen/ops/quantize_per_tensor_native.h>
#include <ATen/ops/quantize_per_tensor_dynamic_native.h>
#include <ATen/ops/quantized_batch_norm_native.h>
#include <ATen/ops/quantized_gru_cell_native.h>
#include <ATen/ops/quantized_lstm_cell_native.h>
#include <ATen/ops/quantized_max_pool1d_native.h>
#include <ATen/ops/quantized_max_pool2d_native.h>
#include <ATen/ops/quantized_max_pool3d_native.h>
#include <ATen/ops/quantized_rnn_relu_cell_native.h>
#include <ATen/ops/quantized_rnn_tanh_cell_native.h>
#include <ATen/ops/rad2deg_native.h>
#include <ATen/ops/rand_native.h>
#include <ATen/ops/rand_like_native.h>
#include <ATen/ops/randint_native.h>
#include <ATen/ops/randint_like_native.h>
#include <ATen/ops/randn_native.h>
#include <ATen/ops/randn_like_native.h>
#include <ATen/ops/random_native.h>
#include <ATen/ops/randperm_native.h>
#include <ATen/ops/range_native.h>
#include <ATen/ops/ravel_native.h>
#include <ATen/ops/real_native.h>
#include <ATen/ops/reciprocal_native.h>
#include <ATen/ops/record_stream_native.h>
#include <ATen/ops/refine_names_native.h>
#include <ATen/ops/reflection_pad1d_native.h>
#include <ATen/ops/reflection_pad1d_backward_native.h>
#include <ATen/ops/reflection_pad2d_native.h>
#include <ATen/ops/reflection_pad2d_backward_native.h>
#include <ATen/ops/reflection_pad3d_native.h>
#include <ATen/ops/reflection_pad3d_backward_native.h>
#include <ATen/ops/relu_native.h>
#include <ATen/ops/relu6_native.h>
#include <ATen/ops/remainder_native.h>
#include <ATen/ops/rename_native.h>
#include <ATen/ops/renorm_native.h>
#include <ATen/ops/repeat_native.h>
#include <ATen/ops/repeat_interleave_native.h>
#include <ATen/ops/replication_pad1d_native.h>
#include <ATen/ops/replication_pad1d_backward_native.h>
#include <ATen/ops/replication_pad2d_native.h>
#include <ATen/ops/replication_pad2d_backward_native.h>
#include <ATen/ops/replication_pad3d_native.h>
#include <ATen/ops/replication_pad3d_backward_native.h>
#include <ATen/ops/requires_grad_native.h>
#include <ATen/ops/reshape_native.h>
#include <ATen/ops/reshape_as_native.h>
#include <ATen/ops/resize_native.h>
#include <ATen/ops/resize_as_native.h>
#include <ATen/ops/resize_as_sparse_native.h>
#include <ATen/ops/resolve_conj_native.h>
#include <ATen/ops/resolve_neg_native.h>
#include <ATen/ops/result_type_native.h>
#include <ATen/ops/retain_grad_native.h>
#include <ATen/ops/retains_grad_native.h>
#include <ATen/ops/rms_norm_native.h>
#include <ATen/ops/rnn_relu_native.h>
#include <ATen/ops/rnn_relu_cell_native.h>
#include <ATen/ops/rnn_tanh_native.h>
#include <ATen/ops/rnn_tanh_cell_native.h>
#include <ATen/ops/roll_native.h>
#include <ATen/ops/rot90_native.h>
#include <ATen/ops/round_native.h>
#include <ATen/ops/row_indices_native.h>
#include <ATen/ops/row_indices_copy_native.h>
#include <ATen/ops/row_stack_native.h>
#include <ATen/ops/rrelu_native.h>
#include <ATen/ops/rrelu_with_noise_native.h>
#include <ATen/ops/rrelu_with_noise_backward_native.h>
#include <ATen/ops/rshift_native.h>
#include <ATen/ops/rsqrt_native.h>
#include <ATen/ops/rsub_native.h>
#include <ATen/ops/scalar_tensor_native.h>
#include <ATen/ops/scaled_dot_product_attention_native.h>
#include <ATen/ops/scatter_native.h>
#include <ATen/ops/scatter_add_native.h>
#include <ATen/ops/scatter_reduce_native.h>
#include <ATen/ops/searchsorted_native.h>
#include <ATen/ops/segment_reduce_native.h>
#include <ATen/ops/select_native.h>
#include <ATen/ops/select_backward_native.h>
#include <ATen/ops/select_copy_native.h>
#include <ATen/ops/select_scatter_native.h>
#include <ATen/ops/selu_native.h>
#include <ATen/ops/set_native.h>
#include <ATen/ops/set_data_native.h>
#include <ATen/ops/sgn_native.h>
#include <ATen/ops/sigmoid_native.h>
#include <ATen/ops/sigmoid_backward_native.h>
#include <ATen/ops/sign_native.h>
#include <ATen/ops/signbit_native.h>
#include <ATen/ops/silu_native.h>
#include <ATen/ops/silu_backward_native.h>
#include <ATen/ops/sin_native.h>
#include <ATen/ops/sinc_native.h>
#include <ATen/ops/sinh_native.h>
#include <ATen/ops/size_native.h>
#include <ATen/ops/slice_native.h>
#include <ATen/ops/slice_backward_native.h>
#include <ATen/ops/slice_copy_native.h>
#include <ATen/ops/slice_inverse_native.h>
#include <ATen/ops/slice_scatter_native.h>
#include <ATen/ops/slogdet_native.h>
#include <ATen/ops/slow_conv3d_native.h>
#include <ATen/ops/slow_conv3d_forward_native.h>
#include <ATen/ops/slow_conv_dilated2d_native.h>
#include <ATen/ops/slow_conv_dilated3d_native.h>
#include <ATen/ops/slow_conv_transpose2d_native.h>
#include <ATen/ops/slow_conv_transpose3d_native.h>
#include <ATen/ops/smm_native.h>
#include <ATen/ops/smooth_l1_loss_native.h>
#include <ATen/ops/smooth_l1_loss_backward_native.h>
#include <ATen/ops/soft_margin_loss_native.h>
#include <ATen/ops/soft_margin_loss_backward_native.h>
#include <ATen/ops/softmax_native.h>
#include <ATen/ops/softplus_native.h>
#include <ATen/ops/softplus_backward_native.h>
#include <ATen/ops/softshrink_native.h>
#include <ATen/ops/softshrink_backward_native.h>
#include <ATen/ops/sort_native.h>
#include <ATen/ops/sparse_bsc_tensor_native.h>
#include <ATen/ops/sparse_bsr_tensor_native.h>
#include <ATen/ops/sparse_compressed_tensor_native.h>
#include <ATen/ops/sparse_coo_tensor_native.h>
#include <ATen/ops/sparse_csc_tensor_native.h>
#include <ATen/ops/sparse_csr_tensor_native.h>
#include <ATen/ops/sparse_dim_native.h>
#include <ATen/ops/sparse_mask_native.h>
#include <ATen/ops/sparse_resize_native.h>
#include <ATen/ops/sparse_resize_and_clear_native.h>
#include <ATen/ops/sparse_sampled_addmm_native.h>
#include <ATen/ops/special_airy_ai_native.h>
#include <ATen/ops/special_bessel_j0_native.h>
#include <ATen/ops/special_bessel_j1_native.h>
#include <ATen/ops/special_bessel_y0_native.h>
#include <ATen/ops/special_bessel_y1_native.h>
#include <ATen/ops/special_chebyshev_polynomial_t_native.h>
#include <ATen/ops/special_chebyshev_polynomial_u_native.h>
#include <ATen/ops/special_chebyshev_polynomial_v_native.h>
#include <ATen/ops/special_chebyshev_polynomial_w_native.h>
#include <ATen/ops/special_digamma_native.h>
#include <ATen/ops/special_entr_native.h>
#include <ATen/ops/special_erf_native.h>
#include <ATen/ops/special_erfc_native.h>
#include <ATen/ops/special_erfcx_native.h>
#include <ATen/ops/special_erfinv_native.h>
#include <ATen/ops/special_exp2_native.h>
#include <ATen/ops/special_expit_native.h>
#include <ATen/ops/special_expm1_native.h>
#include <ATen/ops/special_gammainc_native.h>
#include <ATen/ops/special_gammaincc_native.h>
#include <ATen/ops/special_gammaln_native.h>
#include <ATen/ops/special_hermite_polynomial_h_native.h>
#include <ATen/ops/special_hermite_polynomial_he_native.h>
#include <ATen/ops/special_i0_native.h>
#include <ATen/ops/special_i0e_native.h>
#include <ATen/ops/special_i1_native.h>
#include <ATen/ops/special_i1e_native.h>
#include <ATen/ops/special_laguerre_polynomial_l_native.h>
#include <ATen/ops/special_legendre_polynomial_p_native.h>
#include <ATen/ops/special_log1p_native.h>
#include <ATen/ops/special_log_ndtr_native.h>
#include <ATen/ops/special_log_softmax_native.h>
#include <ATen/ops/special_logit_native.h>
#include <ATen/ops/special_logsumexp_native.h>
#include <ATen/ops/special_modified_bessel_i0_native.h>
#include <ATen/ops/special_modified_bessel_i1_native.h>
#include <ATen/ops/special_modified_bessel_k0_native.h>
#include <ATen/ops/special_modified_bessel_k1_native.h>
#include <ATen/ops/special_multigammaln_native.h>
#include <ATen/ops/special_ndtr_native.h>
#include <ATen/ops/special_ndtri_native.h>
#include <ATen/ops/special_polygamma_native.h>
#include <ATen/ops/special_psi_native.h>
#include <ATen/ops/special_round_native.h>
#include <ATen/ops/special_scaled_modified_bessel_k0_native.h>
#include <ATen/ops/special_scaled_modified_bessel_k1_native.h>
#include <ATen/ops/special_shifted_chebyshev_polynomial_t_native.h>
#include <ATen/ops/special_shifted_chebyshev_polynomial_u_native.h>
#include <ATen/ops/special_shifted_chebyshev_polynomial_v_native.h>
#include <ATen/ops/special_shifted_chebyshev_polynomial_w_native.h>
#include <ATen/ops/special_sinc_native.h>
#include <ATen/ops/special_softmax_native.h>
#include <ATen/ops/special_spherical_bessel_j0_native.h>
#include <ATen/ops/special_xlog1py_native.h>
#include <ATen/ops/special_xlogy_native.h>
#include <ATen/ops/special_zeta_native.h>
#include <ATen/ops/split_native.h>
#include <ATen/ops/split_copy_native.h>
#include <ATen/ops/split_with_sizes_native.h>
#include <ATen/ops/split_with_sizes_copy_native.h>
#include <ATen/ops/sqrt_native.h>
#include <ATen/ops/square_native.h>
#include <ATen/ops/squeeze_native.h>
#include <ATen/ops/squeeze_copy_native.h>
#include <ATen/ops/sspaddmm_native.h>
#include <ATen/ops/stack_native.h>
#include <ATen/ops/std_native.h>
#include <ATen/ops/std_mean_native.h>
#include <ATen/ops/stft_native.h>
#include <ATen/ops/stride_native.h>
#include <ATen/ops/sub_native.h>
#include <ATen/ops/subtract_native.h>
#include <ATen/ops/sum_native.h>
#include <ATen/ops/sum_to_size_native.h>
#include <ATen/ops/svd_native.h>
#include <ATen/ops/swapaxes_native.h>
#include <ATen/ops/swapdims_native.h>
#include <ATen/ops/sym_constrain_range_native.h>
#include <ATen/ops/sym_constrain_range_for_size_native.h>
#include <ATen/ops/sym_numel_native.h>
#include <ATen/ops/sym_size_native.h>
#include <ATen/ops/sym_storage_offset_native.h>
#include <ATen/ops/sym_stride_native.h>
#include <ATen/ops/t_native.h>
#include <ATen/ops/t_copy_native.h>
#include <ATen/ops/take_native.h>
#include <ATen/ops/take_along_dim_native.h>
#include <ATen/ops/tan_native.h>
#include <ATen/ops/tanh_native.h>
#include <ATen/ops/tanh_backward_native.h>
#include <ATen/ops/tensor_split_native.h>
#include <ATen/ops/tensordot_native.h>
#include <ATen/ops/thnn_conv2d_native.h>
#include <ATen/ops/threshold_native.h>
#include <ATen/ops/threshold_backward_native.h>
#include <ATen/ops/tile_native.h>
#include <ATen/ops/to_native.h>
#include <ATen/ops/to_dense_native.h>
#include <ATen/ops/to_dense_backward_native.h>
#include <ATen/ops/to_mkldnn_native.h>
#include <ATen/ops/to_mkldnn_backward_native.h>
#include <ATen/ops/to_padded_tensor_native.h>
#include <ATen/ops/to_sparse_native.h>
#include <ATen/ops/to_sparse_bsc_native.h>
#include <ATen/ops/to_sparse_bsr_native.h>
#include <ATen/ops/to_sparse_csc_native.h>
#include <ATen/ops/to_sparse_csr_native.h>
#include <ATen/ops/topk_native.h>
#include <ATen/ops/trace_native.h>
#include <ATen/ops/trace_backward_native.h>
#include <ATen/ops/transpose_native.h>
#include <ATen/ops/transpose_copy_native.h>
#include <ATen/ops/trapezoid_native.h>
#include <ATen/ops/trapz_native.h>
#include <ATen/ops/triangular_solve_native.h>
#include <ATen/ops/tril_native.h>
#include <ATen/ops/tril_indices_native.h>
#include <ATen/ops/triplet_margin_loss_native.h>
#include <ATen/ops/triu_native.h>
#include <ATen/ops/triu_indices_native.h>
#include <ATen/ops/true_divide_native.h>
#include <ATen/ops/trunc_native.h>
#include <ATen/ops/type_as_native.h>
#include <ATen/ops/unbind_native.h>
#include <ATen/ops/unbind_copy_native.h>
#include <ATen/ops/unflatten_native.h>
#include <ATen/ops/unflatten_dense_tensors_native.h>
#include <ATen/ops/unfold_native.h>
#include <ATen/ops/unfold_backward_native.h>
#include <ATen/ops/unfold_copy_native.h>
#include <ATen/ops/uniform_native.h>
#include <ATen/ops/unique_consecutive_native.h>
#include <ATen/ops/unique_dim_native.h>
#include <ATen/ops/unique_dim_consecutive_native.h>
#include <ATen/ops/unsafe_chunk_native.h>
#include <ATen/ops/unsafe_split_native.h>
#include <ATen/ops/unsafe_split_with_sizes_native.h>
#include <ATen/ops/unsqueeze_native.h>
#include <ATen/ops/unsqueeze_copy_native.h>
#include <ATen/ops/upsample_bicubic2d_native.h>
#include <ATen/ops/upsample_bicubic2d_backward_native.h>
#include <ATen/ops/upsample_bilinear2d_native.h>
#include <ATen/ops/upsample_bilinear2d_backward_native.h>
#include <ATen/ops/upsample_linear1d_native.h>
#include <ATen/ops/upsample_linear1d_backward_native.h>
#include <ATen/ops/upsample_nearest1d_native.h>
#include <ATen/ops/upsample_nearest1d_backward_native.h>
#include <ATen/ops/upsample_nearest2d_native.h>
#include <ATen/ops/upsample_nearest2d_backward_native.h>
#include <ATen/ops/upsample_nearest3d_native.h>
#include <ATen/ops/upsample_nearest3d_backward_native.h>
#include <ATen/ops/upsample_trilinear3d_native.h>
#include <ATen/ops/upsample_trilinear3d_backward_native.h>
#include <ATen/ops/value_selecting_reduction_backward_native.h>
#include <ATen/ops/values_native.h>
#include <ATen/ops/values_copy_native.h>
#include <ATen/ops/vander_native.h>
#include <ATen/ops/var_native.h>
#include <ATen/ops/var_mean_native.h>
#include <ATen/ops/vdot_native.h>
#include <ATen/ops/view_native.h>
#include <ATen/ops/view_as_native.h>
#include <ATen/ops/view_as_complex_native.h>
#include <ATen/ops/view_as_complex_copy_native.h>
#include <ATen/ops/view_as_real_native.h>
#include <ATen/ops/view_as_real_copy_native.h>
#include <ATen/ops/view_copy_native.h>
#include <ATen/ops/vsplit_native.h>
#include <ATen/ops/vstack_native.h>
#include <ATen/ops/where_native.h>
#include <ATen/ops/xlogy_native.h>
#include <ATen/ops/xor_native.h>
#include <ATen/ops/zero_native.h>
#include <ATen/ops/zeros_native.h>
#include <ATen/ops/zeros_like_native.h>


