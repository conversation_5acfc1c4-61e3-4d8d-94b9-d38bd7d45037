clc
clear

%% 导入数据

Data = xlsread('小麦248.xlsx');
data = Data(2:end,:);  %光谱阵和理化值
nm = Data(1,:);   %波段
X = data(:,1:end-1);    %光谱阵
y = data(:,end);       %理化值

%% K-S划分数据
Num = round(size(X,1)*0.75);
[XSelected,XRest,vSelectedRowIndex,vNotSelectedSample] = ks(X,Num);
ySelected = y(vSelectedRowIndex);
yRest = y(vNotSelectedSample);

Xtrain = XSelected;
Xtest = XRest;
Ytrain = ySelected;
Ytest = yRest;

% 输出训练集和测试集的大小
fprintf('训练集特征大小：%d x %d\n', size(Xtrain));
fprintf('训练集标签大小：%d x 1\n', size(Ytrain));
fprintf('测试集特征大小：%d x %d\n', size(Xtest));
fprintf('测试集标签大小：%d x 1\n', size(Ytest));

%% 划分统计结果
% 计算统计信息
train_stats = [size(Ytrain, 1), max(Ytrain), min(Ytrain), mean(Ytrain), std(Ytrain)];
test_stats = [size(Ytest, 1), max(Ytest), min(Ytest), mean(Ytest), std(Ytest)];
all_stats = [size(y, 1), max(y), min(y), mean(y), std(y)];
% 创建表格
table_data = [train_stats; test_stats; all_stats];
T = array2table(table_data, 'VariableNames', {'SampleCount', 'MaxValue', 'MinValue', 'Mean', 'StdDev'}, ...
    'RowNames', {'TrainSet', 'TestSet', 'AllSamples'});
% 显示表格
disp(T);
