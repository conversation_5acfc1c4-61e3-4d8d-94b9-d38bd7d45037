#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
SpectraTool 模块功能测试脚本
测试各个功能模块是否正常工作
"""

import numpy as np
import pandas as pd
import scipy.io as sio
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_data_loading():
    """测试数据加载"""
    print("=" * 50)
    print("测试数据加载...")
    try:
        # 加载.mat文件
        data = sio.loadmat('data/spectra_data.mat')
        print(f"✓ 成功加载.mat文件")
        print(f"  数据键: {list(data.keys())}")
        
        # 检查数据结构
        if 'NIR' in data:
            X = data['NIR']
            print(f"  光谱数据形状: {X.shape}")
        if 'octane' in data:
            y = data['octane']
            print(f"  标签数据形状: {y.shape}")
            
        return True
    except Exception as e:
        print(f"✗ 数据加载失败: {e}")
        return False

def test_preprocessing():
    """测试预处理模块"""
    print("=" * 50)
    print("测试预处理模块...")
    try:
        from preprocess.msc import msc
        from preprocess.snv import snv
        from preprocess.savgol import savgol
        
        # 创建测试数据
        X_test = np.random.rand(10, 100)
        
        # 测试MSC
        X_msc = msc(X_test)
        print(f"✓ MSC预处理成功，输出形状: {X_msc.shape}")
        
        # 测试SNV
        X_snv = snv(X_test)
        print(f"✓ SNV预处理成功，输出形状: {X_snv.shape}")
        
        # 测试Savgol
        X_sg = savgol(X_test, window_length=11, polyorder=2)
        print(f"✓ Savgol滤波成功，输出形状: {X_sg.shape}")
        
        return True
    except Exception as e:
        print(f"✗ 预处理模块测试失败: {e}")
        return False

def test_wavelength_selection():
    """测试波长选择模块"""
    print("=" * 50)
    print("测试波长选择模块...")
    try:
        from wavelength_selection.spa import spa
        
        # 创建测试数据
        X_test = np.random.rand(50, 100)
        y_test = np.random.rand(50)
        
        # 测试SPA
        selected_indices = spa(X_test, y_test, n_selected=10)
        print(f"✓ SPA波长选择成功，选择了 {len(selected_indices)} 个波长")
        print(f"  选择的波长索引: {selected_indices[:5]}...")
        
        return True
    except Exception as e:
        print(f"✗ 波长选择模块测试失败: {e}")
        return False

def test_modeling():
    """测试建模模块"""
    print("=" * 50)
    print("测试建模模块...")
    try:
        from sklearn.cross_decomposition import PLSRegression
        from sklearn.ensemble import RandomForestRegressor
        from sklearn.model_selection import train_test_split
        from sklearn.metrics import r2_score, mean_squared_error
        
        # 创建测试数据
        X_test = np.random.rand(100, 50)
        y_test = np.random.rand(100)
        
        X_train, X_test_split, y_train, y_test_split = train_test_split(
            X_test, y_test, test_size=0.3, random_state=42
        )
        
        # 测试PLS回归
        pls = PLSRegression(n_components=5)
        pls.fit(X_train, y_train)
        y_pred_pls = pls.predict(X_test_split)
        r2_pls = r2_score(y_test_split, y_pred_pls)
        print(f"✓ PLS回归测试成功，R² = {r2_pls:.3f}")
        
        # 测试随机森林
        rf = RandomForestRegressor(n_estimators=50, random_state=42)
        rf.fit(X_train, y_train)
        y_pred_rf = rf.predict(X_test_split)
        r2_rf = r2_score(y_test_split, y_pred_rf)
        print(f"✓ 随机森林测试成功，R² = {r2_rf:.3f}")
        
        return True
    except Exception as e:
        print(f"✗ 建模模块测试失败: {e}")
        return False

def test_utils():
    """测试工具模块"""
    print("=" * 50)
    print("测试工具模块...")
    try:
        from utils.plot_style import apply_plot_style
        import matplotlib.pyplot as plt
        
        # 创建测试图像
        fig, ax = plt.subplots()
        ax.plot([1, 2, 3], [1, 4, 2])
        ax.set_title("测试图像")
        
        # 测试样式应用
        apply_plot_style(ax, theme='light')
        print("✓ 浅色主题样式应用成功")
        
        apply_plot_style(ax, theme='dark')
        print("✓ 深色主题样式应用成功")
        
        plt.close(fig)
        return True
    except Exception as e:
        print(f"✗ 工具模块测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("SpectraTool 模块功能测试")
    print("=" * 50)
    
    tests = [
        ("数据加载", test_data_loading),
        ("预处理模块", test_preprocessing),
        ("波长选择", test_wavelength_selection),
        ("建模模块", test_modeling),
        ("工具模块", test_utils)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name}测试出现异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("=" * 50)
    print("测试总结:")
    passed = 0
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有模块测试通过！SpectraTool 已准备就绪。")
    else:
        print("⚠️  部分模块测试失败，请检查相关模块。")

if __name__ == "__main__":
    main()
