#!/usr/bin/env python 
# -*- coding:utf-8 -*-
__author__ = '<PERSON><PERSON> @ Shanxi University'

import scipy.io as sio
import numpy as np
from sklearn.preprocessing import MinMaxScaler
from sklearn.cross_decomposition import PLSRegression
from sklearn.metrics import mean_squared_error
from sklearn.metrics import r2_score
import matplotlib.pyplot as plt

# 导入数据集
file = 'spectra_data.mat'
data = sio.loadmat(file)
X = data['NIR']
Y = data['octane']
print(X.shape)
print(Y.shape)

# 划分训练集/测试集
# k = np.random.permutation(X.shape[0])
k = np.arange(60)
print(k)
X_train = X[k[:50], :]      # 训练集
Y_train = Y[k[:50], :]

X_test = X[k[50:], :]       # 测试集
Y_test = Y[k[50:], :]

n = Y_test.shape[0]

print(X_train.shape)
print(X_test.shape)
print(Y_train.shape)
print(Y_test.shape)

# 数据归一化
mms = MinMaxScaler()
X_train_mms = mms.fit_transform(X_train)
X_test_mms = mms.transform(X_test)

Y_train_mms = mms.fit_transform(Y_train)

# 建立PLS回归模型
model = PLSRegression(n_components=4)

# 训练PLS回归模型
model.fit(X_train_mms, Y_train_mms)

# PLS回归模型预测
Y_sim_mms = model.predict(X_test_mms)

# 反归一化
Y_sim = mms.inverse_transform(Y_sim_mms.reshape(n, 1))

# 结果
Result = np.hstack((Y_test, Y_sim))
MSE = mean_squared_error(Y_test, Y_sim)
R2 = r2_score(Y_test, Y_sim)

print(Result)
print('Prediction Mean Squared Error (MSE) is {:f}'.format(MSE))
print('Prediction Determined Coefficient R2 is {:f}'.format(R2))

# 绘图
plt.rcParams['font.sans-serif']=['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.figure()
plt.scatter(Y_test, Y_sim)
plt.xlabel('真实值')
plt.ylabel('预测值')
plt.title('汽油辛烷值预测结果（R2={:f})'.format(R2))
plt.plot(Y_test, Y_test, 'r')
plt.show()

