from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QPushButton, QFileDialog, QLabel, QHBoxLayout, QCheckBox
)
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.metrics import r2_score, mean_squared_error, accuracy_score
from sklearn.cross_decomposition import PLSRegression
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
from sklearn.svm import SVR, SVC
from xgboost import XGBRegressor, XGBClassifier
import csv
import os

class BatchModelingTab(QWidget):
    def __init__(self):
        super().__init__()
        self.X = None
        self.y = None
        self.task_type = "Regression"
        self.results = []

        layout = QVBoxLayout()

        self.cb_pls = QCheckBox("PLSR")
        self.cb_rf = QCheckBox("RF")
        self.cb_svm = QCheckBox("SVM")
        self.cb_xgb = QCheckBox("XGB")
        self.cb_classification = QCheckBox("分类任务（勾选表示分类）")

        load_btn = QPushButton("导入 X / y 数据")
        load_btn.clicked.connect(self.load_data)

        run_btn = QPushButton("批量运行")
        run_btn.clicked.connect(self.run_batch)

        export_btn = QPushButton("导出结果为 CSV")
        export_btn.clicked.connect(self.export_csv)

        model_layout = QHBoxLayout()
        model_layout.addWidget(self.cb_pls)
        model_layout.addWidget(self.cb_rf)
        model_layout.addWidget(self.cb_svm)
        model_layout.addWidget(self.cb_xgb)
        model_layout.addStretch()

        layout.addWidget(self.cb_classification)
        layout.addWidget(load_btn)
        layout.addLayout(model_layout)
        layout.addWidget(run_btn)
        layout.addWidget(export_btn)
        self.setLayout(layout)

    def load_data(self):
        x_path, _ = QFileDialog.getOpenFileName(self, "选择 X 数据", "", "CSV Files (*.csv)")
        y_path, _ = QFileDialog.getOpenFileName(self, "选择 y 数据", "", "CSV Files (*.csv)")
        if x_path and y_path:
            x_df = pd.read_csv(x_path)
            y_df = pd.read_csv(y_path)
            self.X = x_df.to_numpy()
            self.y = y_df.to_numpy().ravel()

    def run_batch(self):
        if self.X is None or self.y is None:
            return
        self.results.clear()
        is_cls = self.cb_classification.isChecked()
        X_train, X_test, y_train, y_test = train_test_split(self.X, self.y, test_size=0.3, random_state=42)

        def evaluate(model_name, model, is_classification=False):
            model.fit(X_train, y_train)
            y_pred = model.predict(X_test)
            if is_classification:
                acc = accuracy_score(y_test, y_pred)
                self.results.append([model_name, "Accuracy", round(acc, 4)])
            else:
                rmse = mean_squared_error(y_test, y_pred, squared=False)
                r2 = r2_score(y_test, y_pred)
                self.results.append([model_name, "RMSE", round(rmse, 4)])
                self.results.append([model_name, "R2", round(r2, 4)])

        if self.cb_pls.isChecked() and not is_cls:
            evaluate("PLSR", PLSRegression(n_components=10))
        if self.cb_rf.isChecked():
            model = RandomForestClassifier() if is_cls else RandomForestRegressor()
            evaluate("RF", model, is_cls)
        if self.cb_svm.isChecked():
            model = SVC() if is_cls else SVR()
            evaluate("SVM", model, is_cls)
        if self.cb_xgb.isChecked():
            model = XGBClassifier() if is_cls else XGBRegressor()
            evaluate("XGB", model, is_cls)

    def export_csv(self):
        if not self.results:
            return
        save_path, _ = QFileDialog.getSaveFileName(self, "保存结果为 CSV", "batch_results.csv", "CSV Files (*.csv)")
        if save_path:
            with open(save_path, mode='w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(["模型", "指标", "得分"])
                writer.writerows(self.results)
