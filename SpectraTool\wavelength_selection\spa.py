import numpy as np
from sklearn.linear_model import Ridge
from sklearn.model_selection import cross_val_score

def spa(X, y, n_selected=10):
    """
    Successive Projections Algorithm (SPA) 简化版
    选择最优的波长变量用于建模
    参数:
        X: 特征矩阵（样本 x 波长）
        y: 响应变量
        n_selected: 要选择的变量个数
    返回:
        selected_indices: 选择的波长索引列表
    """
    n_samples, n_wavelengths = X.shape
    selected = []
    remaining = list(range(n_wavelengths))

    # 初始选择相关性最强的波长
    corrs = np.abs(np.corrcoef(X.T, y)[-1, :-1])
    first_idx = np.argmax(corrs)
    selected.append(first_idx)
    remaining.remove(first_idx)

    while len(selected) < n_selected:
        best_score = -np.inf
        best_idx = None
        for idx in remaining:
            temp_indices = selected + [idx]
            score = np.mean(cross_val_score(Ridge(), X[:, temp_indices], y, cv=5))
            if score > best_score:
                best_score = score
                best_idx = idx
        selected.append(best_idx)
        remaining.remove(best_idx)

    return selected
