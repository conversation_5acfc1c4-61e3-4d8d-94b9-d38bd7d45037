from xgboost import XGBClassifier
from sklearn.metrics import accuracy_score, confusion_matrix

class XGBModel:
    def __init__(self, n_estimators=100, learning_rate=0.1):
        self.model = XGBClassifier(n_estimators=n_estimators, learning_rate=learning_rate, use_label_encoder=False, eval_metric='logloss')

    def fit(self, X, y):
        self.model.fit(X, y)

    def predict(self, X):
        return self.model.predict(X)

    def evaluate(self, X, y):
        y_pred = self.predict(X)
        return {
            "Accuracy": accuracy_score(y, y_pred),
            "ConfusionMatrix": confusion_matrix(y, y_pred).tolist()
        }
