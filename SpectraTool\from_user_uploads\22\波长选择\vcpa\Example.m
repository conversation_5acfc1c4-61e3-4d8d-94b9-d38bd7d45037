%% 清空
clc
clear

%% 导入数据 运行前先把数据格式调整，第一行为全波长点，行为样品，列为波长反射率，最后一列为理化值y
Data = xlsread('小麦248.xlsx');
data = Data(2:end,:);  %光谱阵和理化值
nm = Data(1,1:end-1);  %读取波长数据，确保维度匹配
X = data(:,1:end-1);    %光谱阵
y = data(:,end);       %理化值

Xtrain = X; %训练集
Ytrain = y; %测试集

%% VCPA
%参数设置
A_max=10;
fold=5;
method='center';
Ratio_Better=0.1;
EDF_Run=50;
BMS_Run=1000;

%波长选择
Result=VCPA(Xtrain,Ytrain,A_max,fold,method,BMS_Run,EDF_Run,Ratio_Better);
Selectedvariables=Result.Vsel; %筛选的特征波长索引
nm_sel = nm(Selectedvariables); %筛选的特征波长

fprintf('特征波长为：%s\n', num2str(nm_sel));
fprintf('特征波长数为：%d\n', length(nm_sel));

%% 绘图
figure,hold,grid off;
plot(nm,X(1,:),'color', '#2878b5','LineWidth', 1);
plot(nm_sel,X(1,SelectedVariables),'ko', 'MarkerSize', 4, 'MarkerFaceColor', 'r')
xlim([730,1100]);
set(gca,'FontSize',12,'Fontname', 'Times New Roman','LineWidth',1,'FontWeight','bold'); 
xlabel('Wavelength (nm)');
ylabel('Reflectance');
box on;

%% 特征波长数据集
%X = X(:,SelectedVariables);


%% 这里为PLS预测（可以不用管）  *******如要运行请输入自己的预测集*********
F=predict(Xtrain,Ytrain,Xtest,Ytest,selected_variables,A_max,fold,method);

% plot of EDF

plot(1:EDF_Run,Result.Ratio)

% plot of RMSECV of EDF run

plot(1:EDF_Run,Result.minRMSECV_EDF)

% hist the distribution of  mean number of the selected variables by BMS

predefinevar=round(sqrt(size(Xtrain,2)));
binary_matrix=generate_binary_matrix(N, BMS_Run,predefinevar);
bar(sum(binary_matrix)) % every variable has the same chance to be sampled 
hist(sum(binary_matrix'))


% 50 replicate running
tic;
for i=1:50
Result=VCPA(Xtrain,Ytrain,A_max,fold,method,BMS_Run,EDF_Run,Ratio_Better);
selected_variables=Result.Vsel;
F=predict(Xtrain,Ytrain,Xtest,Ytest,selected_variables,A_max,fold,method);
RMSEP(i)=F.RMSEP;
RMSEC(i)=F.RMSEC;
number(i)=length(selected_variables);
variables{i}=selected_variables;
RMSECV_EDF(i,:)=Result.minRMSECV_EDF;
fprintf('The %d(th) run has finished,elapsed time is %g seconds!!\n', i,toc);       
end

% plot of RMSECV of EDF run
plot(1:EDF_Run,mean(RMSECV_EDF))

