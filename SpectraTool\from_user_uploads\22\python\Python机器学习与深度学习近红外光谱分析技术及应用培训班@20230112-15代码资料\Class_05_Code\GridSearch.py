#!/usr/bin/env python 
# -*- coding:utf-8 -*-
__author__ = '<PERSON><PERSON> @ Shanxi University'

from sklearn.svm import SVR
import numpy as np
from sklearn.model_selection import train_test_split
import scipy.io as sio
from sklearn import preprocessing
from sklearn.metrics import mean_squared_error
from sklearn.metrics import r2_score
import matplotlib.pylab as pl
from sklearn.model_selection import GridSearchCV
from sklearn.pipeline import make_pipeline

# 导入数据集
file = 'spectra_data.mat'
data = sio.loadmat(file)
X = data['NIR']
Y = data['octane']
print(X.shape)
print(Y.shape)

# 划分训练集/测试集
X_train, X_test, Y_train, Y_test = train_test_split(
    X, Y, test_size=0.3, random_state=None)

n = Y_test.shape[0]
print(n)

# 数据归一化
mms = preprocessing.MinMaxScaler()
X_train_mms = mms.fit_transform(X_train)
X_test_mms = mms.transform(X_test)

Y_train_mms = mms.fit_transform(Y_train)


# 建立SVR模型
svm = SVR(kernel='rbf', degree=3, gamma='auto',
                 coef0=0.0, tol=1e-5, C=1.0, epsilon=0.1, shrinking=True,
                 cache_size=200, verbose=False, max_iter=-1)

# 训练SVR模型
svm.fit(X_train_mms, Y_train_mms.ravel())

# SVR模型预测
Y_sim_mms = svm.predict(X_test_mms)

# 反归一化
Y_sim = mms.inverse_transform(Y_sim_mms.reshape(n, -1))

# 结果
Result = np.hstack((Y_test, Y_sim))
MSE = mean_squared_error(Y_test, Y_sim)
R2 = r2_score(Y_test, Y_sim)

# print(Result)
print('[Without Grid Search] Prediction Mean Squared Error (MSE) is {:f}'.format(MSE))
print('[Without Grid Search] Prediction Determined Coefficient R2 is {:f}'.format(R2))

# 绘图
pl.rcParams['font.sans-serif']=['SimHei']
pl.rcParams['axes.unicode_minus'] = False
pl.figure()
pl.scatter(Y_test, Y_sim)
pl.xlabel('真实值')
pl.ylabel('预测值')
pl.title('汽油辛烷值预测结果（R2={:f})'.format(R2))
pl.plot(Y_test, Y_test, 'r')
pl.show()


# 网格搜索
param_grid = {'C': [0.001, 0.01, 0.1, 1, 10, 100],'gamma': [0.001, 0.01, 0.1, 1, 10, 100]}
gs = GridSearchCV(SVR(), param_grid, cv=5)
gs = gs.fit(X_train_mms, Y_train_mms.ravel())
print(gs.best_score_)
print(gs.best_params_)

reg = gs.best_estimator_
reg.fit(X_train_mms, Y_train_mms.ravel())

# SVR模型预测
Y_sim_mms = reg.predict(X_test_mms)

# 反归一化
Y_sim = mms.inverse_transform(Y_sim_mms.reshape(n, -1))

# 结果
Result = np.hstack((Y_test, Y_sim))
MSE = mean_squared_error(Y_test, Y_sim)
R2 = r2_score(Y_test, Y_sim)

print('[With Grid Search] Prediction Mean Squared Error (MSE) is {:f}'.format(MSE))
print('[With Grid Search] Prediction Determined Coefficient R2 is {:f}'.format(R2))

# 绘图
pl.rcParams['font.sans-serif']=['SimHei']
pl.rcParams['axes.unicode_minus'] = False
pl.figure()
pl.scatter(Y_test, Y_sim)
pl.xlabel('真实值')
pl.ylabel('预测值')
pl.title('汽油辛烷值预测结果（R2={:f})'.format(R2))
pl.plot(Y_test, Y_test, 'r')
pl.show()