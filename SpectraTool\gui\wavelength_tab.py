from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QPushButton, QFileDialog, QSpinBox, QLabel, QHBoxLayout
)
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
import matplotlib.pyplot as plt
import numpy as np
from utils.plot_style import apply_plot_style
import pandas as pd
from wavelength_selection.spa import spa

class WavelengthTab(QWidget):
    def __init__(self):
        super().__init__()
        self.X = None
        self.y = None
        self.wavelengths = None
        self.selected = []

        layout = QVBoxLayout()

        load_btn = QPushButton("导入光谱 + 响应数据")
        load_btn.clicked.connect(self.load_data)

        self.n_spin = QSpinBox()
        self.n_spin.setMinimum(1)
        self.n_spin.setMaximum(100)
        self.n_spin.setValue(10)

        run_btn = QPushButton("运行 SPA 波长选择")
        run_btn.clicked.connect(self.run_spa)

        control_layout = QHBoxLayout()
        control_layout.addWidget(QLabel("选择变量个数："))
        control_layout.addWidget(self.n_spin)
        control_layout.addStretch()

        layout.addWidget(load_btn)
        layout.addLayout(control_layout)
        layout.addWidget(run_btn)

        # 图像显示
        self.canvas = FigureCanvas(plt.Figure(figsize=(8, 4)))
        layout.addWidget(self.canvas)
        self.ax = self.canvas.figure.subplots()

        self.setLayout(layout)

    def load_data(self):
        x_path, _ = QFileDialog.getOpenFileName(self, "选择光谱数据 (X)", "", "CSV Files (*.csv)")
        y_path, _ = QFileDialog.getOpenFileName(self, "选择响应变量 (y)", "", "CSV Files (*.csv)")
        if x_path and y_path:
            x_df = pd.read_csv(x_path)
            y_df = pd.read_csv(y_path)
            self.wavelengths = x_df.columns.astype(float)
            self.X = x_df.to_numpy()
            self.y = y_df.to_numpy().ravel()
            self.ax.clear()
            for row in self.X:
                self.ax.plot(self.wavelengths, row, color='gray', alpha=0.3)
            self.ax.set_title("原始光谱")
            self.ax.set_xlabel("波长")
            self.ax.set_ylabel("吸光度")
            self.canvas.draw()

    def run_spa(self):
        if self.X is None or self.y is None:
            return
        n = self.n_spin.value()
        self.selected = spa(self.X, self.y, n_selected=n)
        self.ax.clear()
        for row in self.X:
            self.ax.plot(self.wavelengths, row, color='gray', alpha=0.3)
        for i in self.selected:
            self.ax.axvline(x=self.wavelengths[i], color='red', linestyle='--')
        self.ax.set_title("SPA 选择的波长位置")
        self.ax.set_xlabel("波长")
        self.ax.set_ylabel("吸光度")
        self.canvas.draw()
